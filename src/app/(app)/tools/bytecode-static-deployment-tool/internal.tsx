"use client";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { App<PERSON><PERSON> } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import Footer from "@/app/components/Footer/Footer";
import { AppTextarea } from "@/app/components/app-textarea";

import { AppCode } from "@/app/components/app-code";


/**
 * Generates the initialization code for a Solidity Smart Contract
 * following the pattern:
 * 
 * PUSH1 <size>     // Size of runtime code
 * PUSH1 <offset>   // Offset where runtime code begins in the full bytecode
 * PUSH1 <memory offset> // Destination offset in memory (0)
 * CODECOPY         // Copy runtime code to memory
 *
 * PUSH1 <size>     // Size for return
 * PUSH1 <memory offset> // Offset for return
 * RETURN           // Return bytes from memory as the runtime code
 * 
 * @param runtimeBytecode - The runtime bytecode of the contract (without the init code)
 * @returns The full initialization code (hexadecimal string)
 */
function generateInitCode(runtimeBytecode: string): string {
  // Remove '0x' prefix if present
  const cleanBytecode = runtimeBytecode.startsWith('0x') 
    ? runtimeBytecode.slice(2) 
    : runtimeBytecode;
  
  // Calculate the size of the runtime bytecode (in bytes)
  const runtimeSize = cleanBytecode.length / 2; // 2 hex chars = 1 byte
  
  // Calculate the offset where runtime code begins
  // In this simple case, it's just the size of the init code
  // Our init code will be ~37 bytes (74 hex chars) in this implementation
  const runtimeOffset = 74;
  
  // Memory offset (typically 0)
  const memoryOffset = 0;
  
  // Convert decimal values to hex
  // Size needs 64 chars (32 bytes) for PUSH32
  // Others need 2 chars (1 byte) for PUSH1
  const runtimeSizeHex = runtimeSize.toString(16).padStart(64, '0');
  const runtimeOffsetHex = runtimeOffset.toString(16).padStart(2, '0');
  const memoryOffsetHex = memoryOffset.toString(16).padStart(2, '0');
  
  // PUSH32 <size>
  const pushSize = `7F${runtimeSizeHex}`;
  
  // PUSH1 <offset>
  const pushOffset = `60${runtimeOffsetHex}`;
  
  // PUSH1 <memory offset>
  const pushMemOffset = `60${memoryOffsetHex}`;
  
  // CODECOPY opcode is 0x39
  const codecopy = '39';
  
  // PUSH32 <size> (again for RETURN)
  const pushSizeForReturn = `7F${runtimeSizeHex}`;
  
  // PUSH1 <memory offset> (again for RETURN)
  const pushMemOffsetForReturn = `60${memoryOffsetHex}`;
  
  // RETURN opcode is 0xf3
  const returnOp = 'f3';
  
  // Combine all parts
  const initCode = pushSize + pushOffset + pushMemOffset + codecopy + 
                   pushSizeForReturn + pushMemOffsetForReturn + returnOp;
  
  // Return the complete bytecode (initCode + runtimeBytecode)
  return '0x' + initCode;
}

export default function BytecodeStaticDeploymentToolInternal() {
  const [bytecode, setBytecode] = useState("");

  const initCode = useMemo(() => {
    return generateInitCode(bytecode)
  }, [bytecode])

  return (
    <div className="min-h-screen bg-dashboardBG">
      <div className="gradient-dark-bg flex items-center justify-between bg-blockBg px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="min-h-[800px] p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Bytecode Static Deployment Tool</AppPageTitle>

         

          <p className="mb-[3px] block text-[15px] leading-[18px] text-textSecondary">
            Given some bytecode, this tool generates the initCode necessary to make it work
          </p>
          <p className="mb-[3px] block text-[15px] leading-[18px] text-textSecondary">
            NOTE: The tool assumes the bytecode has no constructor!
          </p>
        </div>
        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppTextarea
              className="mb-[8px]"
              label={"Contract Bytecode"}
              value={bytecode}
              onChange={(e) => setBytecode(e.target.value)}
              type="text"
            />
          </div>
        </div>

        <h3 className="text-textPrimary">Encoded</h3>
        <AppCode showLineNumbers={false} code={initCode} language="python" />
        <h3 className="text-textPrimary">Both</h3>
        <AppCode showLineNumbers={false} code={`${initCode}${bytecode}`} language="python" />
      </div>
      <Footer />
    </div>
  );
}
