"use client";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

import { AppLogo } from "@/app/components/app-logo";
import { GradientWrapper } from "@/app/components/gradient-wrapper";
import SandboxBuilder from "@/app/components/SandboxBuilder/SandboxBuilder";

export default function FoundrySandboxInternal() {
  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <div className="gradient-dark-bg flex items-center justify-between bg-blockBg px-[40px] py-[20px]">
          <Link href="/dashboard" className="cursor-pointer">
            <AppLogo />
          </Link>
        </div>

        <GradientWrapper
          useMainBg={true}
          className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8"
        >
          <div className="w-full max-w-6xl rounded-[20px] bg-[#1A1F23] px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/"
                className="mb-6 inline-flex items-center text-white transition-colors duration-200 hover:text-gray-300"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <h1 className="main-title-custom mb-6 text-[40px] font-bold leading-[44px] tracking-normal lg:text-[56px] lg:leading-[60px]">
                Invariant Testing Builder
              </h1>

              <p className="text-[18px] font-thin leading-[24px] text-white lg:text-[22px] lg:leading-[28px]">
                The sandbox allows you to Scaffold Invariant Tests from your Contract ABIs.
              </p>
              <p className="text-[18px] font-thin leading-[24px] text-white lg:text-[22px] lg:leading-[28px]">
                Log in to Recon to scaffold bigger projects with advanced features.
              </p>
            </div>

            <SandboxBuilder />
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
