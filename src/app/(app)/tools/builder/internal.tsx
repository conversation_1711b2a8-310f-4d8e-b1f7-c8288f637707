"use client";
import Link from "next/link";
import { AppLogo } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import Footer from "@/app/components/Footer/Footer";
import SandboxBuilder from "@/app/components/SandboxBuilder/SandboxBuilder";

export default function FoundrySandboxInternal() {
  return (
    <div className="min-h-screen bg-dashboardBG">
      <div className="gradient-dark-bg flex items-center justify-between bg-blockBg px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="min-h-[calc(100vh-442px)] p-[45px]">
        <AppPageTitle className="mb-[20px]">Sandbox</AppPageTitle>
        <h3 className="mb-[16px] text-[16px] leading-[19px] text-textPrimary">
          The sandbox allows you to Scaffold Invariant Tests, Log in into Recon
          to scaffold bigger projects
        </h3>
        <SandboxBuilder />
      </div>
      <Footer />
    </div>
  );
}
