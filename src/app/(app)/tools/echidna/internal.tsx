"use client";
import { ENV_TYPE } from "@/app/app.constants";
import ToolPageLayout from "@/app/components/ToolPageLayout";

export default function EchidnaParserInternal() {
  return (
    <ToolPageLayout
      toolType={ENV_TYPE.ECHIDNA}
      toolName="Echidna Logs Scraper"
      toolDescription={[
        "This tool allows to scrape echidna logs for broken properties repros",
        "Paste your raw echidna logs, and the tool will generate foundry repros for you",
      ]}
      youtubeUrl="https://www.youtube.com/embed/DHAvBrsITRU"
      youtubeOverlayText="Learn how to use Echidna"
    />
  );
}
