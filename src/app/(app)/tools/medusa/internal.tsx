"use client";
import { ENV_TYPE } from "@/app/app.constants";
import ToolPageLayout from "@/app/components/ToolPageLayout";

export default function MedusaParserInternal() {
  return (
    <ToolPageLayout
      toolType={ENV_TYPE.MEDUSA}
      toolName="Medusa Logs Scraper"
      toolDescription={[
        "This tool allows to scrape medusa logs for broken properties repros",
        "Paste your raw medusa logs, and the tool will generate foundry repros for you",
      ]}
      youtubeUrl="https://www.youtube.com/embed/8-qWL2Dcgpc"
      youtubeOverlayText="Learn how to use Medusa"
    />
  );
}
