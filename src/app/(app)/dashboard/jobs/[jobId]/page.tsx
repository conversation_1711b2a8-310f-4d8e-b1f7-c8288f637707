"use client";
import SingleJobContainer from "@/app/components/SingleJobContainer";
import { useGetJobById } from "@/app/services/jobs.hooks";
import { useGetShareByJobId } from "@/app/services/shares.hook";

export default function SingleJobPage({
  params: { jobId },
}: {
  params: { jobId: string };
}) {
  const { data: jobData, isLoading } = useGetJobById(jobId);
  const {
    data: shareInfo,
    isLoading: isJobInfoLoading,
    refetch: reloadShares,
  } = useGetShareByJobId(jobId);

  // TODO: Make share

  // TODO: Fetch if shared

  // Get single Share -> You can just fetch all for now and check if one of them matches
  // Long term: Separate fetching all (Separate page, super expensive)
  // With fetching one, fast and simple

  return (
    <div>
      <SingleJobContainer
        isJobInfoLoading={isJobInfoLoading}
        shareInfo={shareInfo}
        jobId={jobId}
        reloadShares={reloadShares}
        jobData={jobData}
        isLoading={isLoading}
      />
    </div>
  );
}

{
  /* 
      <h2 className="mb-[28px] text-[22px] leading-[26px] text-textPrimary">
        Corpus
      </h2> */
}
