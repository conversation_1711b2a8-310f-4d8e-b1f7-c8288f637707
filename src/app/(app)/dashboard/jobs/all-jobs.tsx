"use client";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react"; // Import useState for managing state

import { AppButton } from "@/app/components/app-button";
import { AppSpinner } from "@/app/components/app-spinner";
import { cn } from "@/app/helpers/cn";
import type { Job } from "@/app/services/jobs.hooks";
import { useGetJobs } from "@/app/services/jobs.hooks";
import { formatDateString } from "@/utils/format";
import axios from "axios";
import styles from "./jobs.module.css";
import type { BrokenPropShow } from "@/app/types/types";
import { formatNumberWithDots, searchObject } from "@/lib/utils";

export const AllJobs = () => {
  const [isEditing, setIsEditing] = useState({});
  const [existingLabels, setExistingLabels] = useState({});
  const { data, isLoading, refetch, isRefetching } = useGetJobs();

  const [showBrokenPropDropdown, setShowBrokenPropDropdown] = useState<
    BrokenPropShow[]
  >([]);

  const renderLink = (url, label, sameWindow = false) => {
    if (!url) return null;
    return (
      <Link
        target={sameWindow ? "" : "_blank"}
        href={url}
        className="text-[15px] font-thin leading-[18px] text-textSecondary"
      >
        {label}
      </Link>
    );
  };

  const buttonDisabled = isLoading || isRefetching;

  const [sortBy, setSortBy] = useState("createdAt");

  function toggleSortBy() {
    setSortBy(sortBy == "createdAt" ? "updatedAt" : "createdAt");
  }

  const [filterNoBroken, setFilterNoBroken] = useState(false);
  function toggleFilterBroken() {
    setFilterNoBroken(!filterNoBroken);
  }

  const [querry, setQuerry] = useState("");

  const sortedJobs = useMemo(() => {
    return (
      data
        ?.sort(
          (jobA, jobB) =>
            new Date(jobB[sortBy]).getTime() - new Date(jobA[sortBy]).getTime()
        )
        // Keywords
        .filter((job) => {
          if (!querry) return true;

          const querryWords = querry.toLowerCase().split(/\s+/);
          return querryWords.every((word) => searchObject(job, word));
        })
        // Filter no broken props
        .filter((job) => (filterNoBroken ? job.testsFailed > 0 : true))
    );
  }, [sortBy, data, querry, filterNoBroken]);

  useEffect(() => {
    if (sortedJobs && sortedJobs.length > 0) {
      setShowBrokenPropDropdown(
        sortedJobs.map((_, index) => ({
          id: index,
          show: false,
        }))
      );
    }
  }, [sortedJobs]);

  const showBrokenPropHandler = (index) => {
    setShowBrokenPropDropdown((prev) =>
      prev.map((trace) => {
        if (trace.id === index) {
          return {
            ...trace,
            show: !trace.show,
          };
        }
        return trace;
      })
    );
  };

  useEffect(() => {
    if (!data) return;

    const newIsEditing = {};
    const newExistingLabels = {};

    data.forEach((job) => {
      newIsEditing[job.id] = false;
      newExistingLabels[job.id] = job.label ? job.label : "";
    });

    setIsEditing(newIsEditing);
    setExistingLabels(newExistingLabels);
  }, [data]);

  const updateJob = async (id: string) => {
    const res = await axios({
      method: "POST",
      url: `/api/jobs/updateLabel`,
      data: {
        newLabel: existingLabels[id],
        jobId: id,
      },
    });
    if (res.data.data === "success") {
      refetch();
    }
  };

  const newLabelHandling = (
    id: string,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setExistingLabels({
      ...existingLabels,
      [id]: e.target.value,
    });
  };

  const handleKeyPress = (
    id: string,
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key === "Enter") {
      updateJob(id);
    }
  };

  const renderDoubleClick = (job: Job) => {
    return (
      <div
        onDoubleClick={() =>
          setIsEditing((prevState) => ({
            ...prevState,
            [job.id]: !prevState[job.id],
          }))
        }
        className={styles.inputContainer}
      >
        {isEditing[job.id] ? (
          <input
            style={{ color: "black" }}
            value={existingLabels[job.id]}
            onChange={(event) => newLabelHandling(job.id, event)}
            onKeyDown={(event) => handleKeyPress(job.id, event)}
          />
        ) : job.label ? (
          job.label
        ) : (
          job.id.slice(0, 4)
        )}
      </div>
    );
  };

  return (
    <div className="mb-[20px] mt-[50px] pr-[45px]">
      <div className="mb-[40px] flex items-center justify-between">
        <h1 className="text-[28px] leading-[33px] text-textPrimary">
          All Jobs
        </h1>

        <div
          className="text-[15px] font-thin leading-[18px] text-textSecondary"
          onClick={() => toggleSortBy()}
        >
          Sorting by {sortBy}
        </div>

        <div
          className="text-[15px] font-thin leading-[18px] text-textSecondary"
          onClick={() => toggleFilterBroken()}
        >
          {filterNoBroken
            ? "Showing only jobs with broken properties"
            : "Showing all jobs"}
        </div>

        <AppButton onClick={refetch} disabled={buttonDisabled}>
          {buttonDisabled ? <AppSpinner /> : "Force Reload"}
        </AppButton>
      </div>
      <div>
        <input
          type="text"
          placeholder="Search names, properties, fuzzer, coverage… everything"
          className="mb-[20px] w-full rounded-[8px] border border-divider p-[10px]"
          value={querry}
          onChange={(e) => setQuerry(e.target.value)}
        />
      </div>

      {sortedJobs?.length > 0 && (
        <div className="flex flex-col gap-[20px]">
          {sortedJobs.map((job, index) => (
            <div
              key={job.id}
              className={cn(
                "flex flex-col py-[20px] px-[22px] justify-between bg-blockBg gradient-dark-bg rounded-t-[8px] border-divider border border-b-0 rounded-b-[8px] border-b-1"
              )}
            >
              <div
                className={`mb-2 w-full text-[18px] leading-[21px] text-textPrimary ${styles.jobs_inputContainer}`}
              >
                {renderDoubleClick(job)}
                <div>
                  {" "}
                  - {job.fuzzer} - {job.repoName} {job.ref} - Created{" "}
                  {formatDateString(job.createdAt)} - Updated{" "}
                  {formatDateString(job.updatedAt)}
                </div>
              </div>
              <div>
                {job.testsFailed != null && job.testsCoverage != null ? (
                  <div className="mb-[3px] flex w-full gap-[25px] text-[15px] font-thin leading-[18px] text-textPrimary ">
                    {job.testsFailed == 0 ? <>🟢</> : <>🔴</>} Failed:{" "}
                    {job.testsFailed} - Coverage: {job.testsCoverage} - Status:{" "}
                    {job.status} {" - "}
                    {/* {job.numberOfTests
                      ? formatNumberWithDots(job.numberOfTests)
                      : ""}{" "} */}
                    {job.progress
                      ? `Progress: ${job.progress ? job.progress : "0"}%  `
                      : ""}
                    {job.eta && job?.progress < 100
                      ? ` - ETA: ${job.eta !== "0m" ? job.eta : "<1m"}  `
                      : ""}
                  </div>
                ) : job.status === "ERROR" ? (
                  <div className="mb-[3px] flex w-full gap-[25px] text-[15px] font-thin leading-[18px] text-textPrimary ">
                    Status: {job.status}
                  </div>
                ) : (
                  ""
                )}
                {showBrokenPropDropdown.length > 0 &&
                  job.brokenProperties &&
                  job.brokenProperties.length > 0 && (
                    <div>
                      <h3
                        onClick={() => showBrokenPropHandler(index)}
                        className="text-white"
                      >
                        {showBrokenPropDropdown.find((el) => el.id === index)
                          ?.show
                          ? "Hide"
                          : "Show"}{" "}
                        broken Props
                      </h3>
                      {showBrokenPropDropdown.find((el) => el.id === index)
                        ?.show ? (
                        <select
                          className="w-auto rounded border border-gray-300 bg-transparent p-2 text-[15px] font-thin leading-[18px] text-white"
                          style={{ minWidth: "fit-content" }}
                        >
                          {job.brokenProperties.map((prop, index) => (
                            <option
                              key={index}
                              value={prop.brokenProperty}
                              className="text-white"
                            >
                              {prop.brokenProperty}
                            </option>
                          ))}
                        </select>
                      ) : (
                        ""
                      )}
                    </div>
                  )}
              </div>
              <div className="flex w-full gap-[25px]">
                {/* {renderLink(job.coverageUrl, "Coverage Report")}
                {renderLink(job.logsUrl, "View Logs")}
                {renderLink(job.corpusUrl, "Download Corpus")} */}
                {renderLink(`/dashboard/jobs/${job.id}`, "View Details")}
                {job.status === "SUCCESS" ||
                job.status === "ERROR" ||
                job.status === "STOPPED"
                  ? renderLink(
                      `/dashboard/jobs/${job.id}/report`,
                      "View Report"
                    )
                  : ""}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
