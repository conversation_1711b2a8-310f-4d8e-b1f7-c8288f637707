"use client";
import axios from "axios";
import { useState } from "react";

import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { AppSpinner } from "@/app/components/app-spinner";

import { cn } from "@/app/helpers/cn";
import { useGetCampaigns } from "@/app/services/campaigns.hook";
import Link from "next/link";

export const AllCampaigns = () => {
  const { data, isLoading, refetch, isRefetching } = useGetCampaigns();

  const buttonDisabled = isLoading || isRefetching;

  const [isButtonLoading, setButtonLoading] = useState(false);

  const handleDelete = async (campaignId: string) => {
    try {
      setButtonLoading(true);
      const foundData = await axios({
        method: "DELETE",
        url: `/api/campaigns/${campaignId}`,
      });

      refetch();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
    setButtonLoading(false);
  };

  const handleToggleComments = async (
    campaignId: string,
    newStatus: boolean
  ) => {
    try {
      setButtonLoading(true);
      await axios({
        method: "POST",
        url: `/api/campaigns/${campaignId}/togglecomments`,
        data: {
          comments: newStatus,
        },
      });

      refetch();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
    setButtonLoading(false);
  };

  return (
    <div className="mb-[20px] mt-[50px] pr-[45px]">
      <div className="mb-[40px] flex items-center justify-between">
        <h1 className="text-[28px] leading-[33px] text-textPrimary">
          All Campaigns
        </h1>

        <AppButton onClick={refetch} disabled={buttonDisabled}>
          {buttonDisabled ? <AppSpinner /> : "Force Reload"}
        </AppButton>
      </div>

      <div>
        <p className="text-[20px] text-textSecondary">
          Listing all Campaigns you created
        </p>
      </div>

      {data?.length > 0 && (
        <div className="flex flex-col gap-[20px]">
          {data.map((campaign) => (
            <div
              key={campaign.id}
              className={cn(
                "py-[20px] px-[22px] justify-between bg-blockBg gradient-dark-bg rounded-t-[8px] border-divider border border-b-0 rounded-b-[8px] border-b-1"
              )}
            >
              <div className="text-[18px] leading-[21px] text-textPrimary">
                <div className="flex flex-col w-full">
                  <div>
                    <p>{campaign.displayName}</p>
                  </div>
                  <div className="flex flex-row justify-between w-full mb-3 mt-3">
                    <AppButton
                      disabled={isButtonLoading}
                      onClick={() => handleDelete(campaign.id)}
                      className={"mb-3"}
                    >
                      {isButtonLoading
                        ? "Loading"
                        : "Delete this Campaign (cannot be reversed)"}
                    </AppButton>
                    <AppButton
                      disabled={isButtonLoading}
                      onClick={() =>
                        handleToggleComments(campaign.id, !campaign.comments)
                      }
                      className={"mb-3"}
                    >
                      {isButtonLoading
                        ? "Loading"
                        : `${
                            campaign.comments ? "Disabled" : "Enable"
                          } comments on PR`}
                    </AppButton>
                    {campaign?.recipes.length > 0
                      ? campaign.recipes.map((recipe, index) => (
                          <Link
                            key={index}
                            href={`/dashboard/recipes/${recipe.id}/manage`}
                          >
                            <AppButton>Manage alerts {index + 1} </AppButton>
                          </Link>
                        ))
                      : ""}
                  </div>
                </div>
                <AppCode
                  code={JSON.stringify(campaign, null, 2)}
                  language="json"
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
