"use client";
import React, { useEffect, useState, useCallback } from "react";
import axios from "axios";
import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import type { Job } from "@/app/services/jobs.hooks";
import type { Organization } from "@/app/services/organization.hooks";
import { SearchDropdown } from "@/app/components/inputs/SearchDropdown";
import config from "@/config";

interface User {
  id: string;
  organizationId: string;
  username?: string;
}

export default function Admin() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [allOrgs, setAllOrgs] = useState<Organization[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [jobId, setJobId] = useState<string | null>(null);
  const [orgInvite, setOrgInvite] = useState<string | null>(null);
  const [displayOrgData, setDisplayOrgData] = useState<boolean>(false);
  const [displayQueuedJobs, setDisplayQueuedJobs] = useState<boolean>(false);
  const [orgData, setOrgData] = useState(null);
  const [allQueuedJobs, setAllQueuedJobs] = useState<Job[]>([]);
  const [runningJobCount, setRunningJobCount] = useState<number>(0);

  const billingStatus = ["UNPAID", "PAID", "REVOKED", "TRIAL"];
  const env =
    config?.github?.app?.installationUrl
      ?.split("https://github.com/apps/")[1]
      ?.split("/installations/new")[0] || "recon";
  const CACHE_KEY_USER = `${env}-users`;
  const CACHE_KEY_ORGS = `${env}-orgs`;
  const CACHE_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

  const getAllOrgs = useCallback(async () => {
    try {
      const cached = localStorage.getItem(CACHE_KEY_ORGS);
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);
        const isValid = Date.now() - timestamp < CACHE_EXPIRY;

        if (isValid) {
          setAllOrgs(data);
          return;
        }
      }

      const res = await axios.get("/api/admin/orgs");
      localStorage.setItem(
        CACHE_KEY_ORGS,
        JSON.stringify({
          data: res.data.data,
          timestamp: Date.now(),
        })
      );
      setAllOrgs(res.data.data);
    } catch (error) {
      console.error("Failed to fetch orgs:", error);
    }
  }, [CACHE_EXPIRY, CACHE_KEY_ORGS]);

  const fetchGithubUsername = async (userId: string) => {
    try {
      const {
        data: {
          data: { login },
        },
      } = await axios({
        method: "POST",
        url: "/api/admin/getGithubUsername",
        data: { userId },
      });
      return login;
    } catch (err) {
      return null;
    }
  };

  const getAllUsers = useCallback(async () => {
    try {
      const cached = localStorage.getItem(CACHE_KEY_USER);
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);
        const isValid = Date.now() - timestamp < CACHE_EXPIRY;

        if (isValid) {
          setUsers(data);
          return;
        }
      }

      const res = await axios.get("/api/admin/users");
      const users = res.data.data;

      const usersWithUsernames = await Promise.all(
        users.map(async (user) => {
          const username = await fetchGithubUsername(user.id);
          return username ? { ...user, username } : user;
        })
      );

      // Cache results
      localStorage.setItem(
        CACHE_KEY_USER,
        JSON.stringify({
          data: usersWithUsernames,
          timestamp: Date.now(),
        })
      );

      setUsers(usersWithUsernames);
    } catch (error) {
      console.error("Failed to fetch users:", error);
    }
  }, [CACHE_EXPIRY, CACHE_KEY_USER]);

  const getQueuedJobs = useCallback(async () => {
    try {
      const res = await axios.get("/api/admin/job/queued");
      setAllQueuedJobs(res.data.data.data);
    } catch (err) {
      console.log("err getting the queued jobs", err);
    }
  }, []);

  const getOrgData = useCallback(async (orgId: string) => {
    const res = await axios({
      method: "POST",
      url: "/api/admin/orgs/info",
      data: {
        orgId: orgId,
      },
    });
    const {
      data: {
        data: { campaigns, jobs, recipes, recurringJobs },
      },
    } = res;
    setOrgData({ campaigns, jobs, recipes, recurringJobs });
  }, []);

  const getRunningJobCount = useCallback(async () => {
    try {
      const res = await axios.get("/api/admin/job/countrunning");
      setRunningJobCount(res.data.data.count);
    } catch (err) {
      console.log("err", err);
    }
  }, []);

  const reset = async () => {
    setSelectedOrg(null);
    setAllOrgs([]);
    setSelectedUser(null);
    setSelectedStatus("");
    await getAllOrgs();
    await getAllUsers();
  };

  const init = useCallback(async () => {
    try {
      const res = await axios.get("/api/admin");
      if (res.status === 200) {
        setIsSuperAdmin(true);
        await getAllOrgs();
        await getAllUsers();
        await getQueuedJobs();
        await getRunningJobCount();
        setSelectedOrg(null);
        setSelectedUser(null);
        setSelectedStatus("");
      }
    } catch (err) {
      console.log("Not Admin");
      window.location.href = "/dashboard"; // Force redirect if not admin
    }
    setIsLoading(false);
  }, [getAllOrgs, getAllUsers, getQueuedJobs, getRunningJobCount]);

  useEffect(() => {
    init();
  }, [init]);

  function handleOrgSelect(value: string): void {
    const org = allOrgs.find((org) => org.id === value);
    setSelectedOrg(org);
    getOrgData(org.id);
  }

  function handleUserSelect(value: string): void {
    const user = users.find((user) => user.id === value);
    setSelectedUser(user);
  }

  function statusHandler(value: string): void {
    if (!value) {
      alert("Please select a status");
      return;
    }
    if (selectedOrg) {
      setSelectedStatus(value);
    }
  }

  const clearCacheHandler = async () => {
    localStorage.removeItem(CACHE_KEY_ORGS);
    localStorage.removeItem(CACHE_KEY_USER);
    await reset();
  };

  async function orgStatusHandler() {
    try {
      await axios({
        method: "POST",
        url: "/api/admin/orgs/setstatus",
        data: {
          orgId: selectedOrg.id,
          status: selectedStatus,
        },
      });

      // Update item in the local storage:
      const data = JSON.parse(localStorage.getItem(CACHE_KEY_ORGS));
      const orgIndex = data.data.findIndex((org) => org.id === selectedOrg.id);
      data.data[orgIndex].billingStatus = selectedStatus;
      localStorage.setItem(CACHE_KEY_ORGS, JSON.stringify(data));

      setSelectedOrg(null);
      setSelectedStatus("");
      alert("Org status switched");
      await reset();
    } catch (err) {
      alert("Fail to switch org status");
    }
  }

  async function createOrgInviteHandler() {
    try {
      const { data } = await axios({
        method: "POST",
        url: "/api/admin/orgs/invite",
        data: {
          orgId: selectedOrg.id,
        },
      });
      setOrgInvite(data.data.code);
    } catch (err) {
      alert("Failed to create org invite");
    }
  }

  async function switchOrgHandler() {
    try {
      await axios({
        method: "POST",
        url: "/api/admin/orgs/switch",
        data: {
          userId: selectedUser.id,
          orgId: selectedOrg.id,
        },
      });
      setSelectedOrg(null);
      setSelectedUser(null);
      alert("Org switched");

      // Update item in the local storage:
      const dataOrgs = JSON.parse(localStorage.getItem(CACHE_KEY_ORGS));
      const orgIndex = dataOrgs.data.findIndex(
        (org) => org.id === selectedOrg.id
      );
      const dataUsers = JSON.parse(localStorage.getItem(CACHE_KEY_USER));
      const userIndex = dataUsers.data.findIndex(
        (user) => user.id === selectedUser.id
      );
      dataUsers.data[userIndex].organizationId = selectedOrg.id;
      localStorage.setItem(CACHE_KEY_ORGS, JSON.stringify(dataOrgs));
      localStorage.setItem(CACHE_KEY_USER, JSON.stringify(dataUsers));

      await reset();
    } catch (err) {
      alert("Failed to switch org");
    }
  }

  async function jobDeleteHandler() {
    try {
      await axios({
        method: "post",
        url: `/api/admin/job/delete`,
        data: {
          jobId: jobId,
        },
      });
      alert("job deleted");
      setJobId("");
    } catch (err) {
      console.log("err", err);
      alert(`Failed to delete job:\n ${err.response.data.message}`);
    }
  }

  async function orgDeleteHandler() {
    // try {
    //   await axios({
    //     method: "POST",
    //     url: `/api/admin/orgs/delete`,
    //     data: {
    //       jobId: jobId,
    //     },
    //   });
    // } catch (err) {
    //   console.log("err", err);
    //   alert(`Failed to delete job:\n ${err.response.data.message}`);
    // }
    alert("NOT implemented yet");
  }

  return (
    <div className="flex w-full flex-col p-8 pl-[45px] pt-[45px]">
      {isLoading ? (
        <p className="mt-[40px] text-[15px] leading-[24px] text-textSecondary">
          Loading...
        </p>
      ) : (
        ""
      )}
      {!isLoading && !isSuperAdmin ? (
        <p className="mt-[40px] text-[15px] leading-[24px] text-textSecondary">
          Not authorized
        </p>
      ) : !isLoading && isSuperAdmin ? (
        <div className="align-center flex flex-col gap-8">
          <div className="flex flex-col justify-around rounded-lg p-6">
            <h2 className="mb-5 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
              Select data
            </h2>
            <div className="flex flex-row">
              {allOrgs.length > 0 ? (
                <div className="mr-[24px] flex flex-col">
                  <h3 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                    Org select
                  </h3>
                  <SearchDropdown
                    items={allOrgs.map((org) => ({
                      label: `${org.id} - ${org.name} - ${org.billingStatus}`,
                      value: org.id,
                    }))}
                    onChange={handleOrgSelect}
                    searchPlaceholder="Search org"
                  />
                </div>
              ) : (
                ""
              )}
              {users.length > 0 ? (
                <div className="flex flex-col">
                  <h3 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                    User select
                  </h3>
                  <SearchDropdown
                    items={users.map((org) => ({
                      label: `${org.id} - ${org.username ?? "n/a"}`,
                      value: org.id,
                    }))}
                    onChange={handleUserSelect}
                    searchPlaceholder="Search user"
                  />
                </div>
              ) : (
                ""
              )}
            </div>
            <div className="mt-4 flex flex-row">
              <AppButton onClick={clearCacheHandler} className="mr-5 w-[200px]">
                Clear cache and reset
              </AppButton>
            </div>
          </div>
          <>
            {allOrgs.length > 0 ? (
              <div className="grid grid-cols-3 gap-6 p-6">
                <div className="flex flex-col">
                  <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                    Paid orgs (
                    {allOrgs.filter((el) => el.billingStatus === "PAID").length}
                    )
                  </h2>
                  <select className="border-border mb-3 h-[41px] min-w-[230px] rounded-[4px] border bg-inputBg pl-[10px] text-textSecondary">
                    <option value="" disabled selected>
                      Paid orgs
                    </option>
                    {allOrgs
                      .filter((el) => el.billingStatus === "PAID")
                      .map((org) => {
                        return (
                          <option key={org.id} className="text-textSecondary">
                            {org.id} - {org.name}
                          </option>
                        );
                      })}
                  </select>
                </div>

                <div className="flex flex-col">
                  <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                    Trial orgs (
                    {
                      allOrgs.filter((el) => el.billingStatus === "TRIAL")
                        .length
                    }
                    )
                  </h2>
                  <select className="border-border mb-3 h-[41px] min-w-[230px] rounded-[4px] border bg-inputBg pl-[10px] text-textSecondary">
                    <option value="" disabled selected>
                      Trial orgs
                    </option>
                    {allOrgs
                      .filter((el) => el.billingStatus === "TRIAL")
                      .map((org) => {
                        return (
                          <option key={org.id} className="text-textSecondary">
                            {org.id} - {org.name}
                          </option>
                        );
                      })}
                  </select>
                </div>
                <div className="flex flex-col">
                  <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                    Unpaid orgs (
                    {
                      allOrgs.filter((el) => el.billingStatus === "UNPAID")
                        .length
                    }
                    )
                  </h2>
                  <select className="border-border mb-3 h-[41px] min-w-[230px] rounded-[4px] border bg-inputBg pl-[10px] text-textSecondary">
                    <option value="" disabled selected>
                      Trial orgs
                    </option>
                    {allOrgs
                      .filter((el) => el.billingStatus === "UNPAID")
                      .map((org) => {
                        return (
                          <option key={org.id} className="text-textSecondary">
                            {org.id} - {org.name}
                          </option>
                        );
                      })}
                  </select>
                </div>
              </div>
            ) : (
              ""
            )}
          </>
          <h2 className="mb-5 cursor-pointer pl-6 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
            Actions:
          </h2>
          <div className="grid grid-cols-4 gap-6 p-6">
            <div className="flex flex-col rounded-lg border border-white/10 bg-gray-800 p-6 shadow-lg shadow-black/20">
              <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                Users in org
              </h2>
              {selectedOrg && selectedOrg.users.length > 0 ? (
                <ul>
                  {selectedOrg.users.map((e) => {
                    return (
                      <li
                        className="mt-[8px] text-[15px] leading-[18px] text-textSecondary"
                        key={e.id}
                      >
                        {e.id} -{" "}
                        {users.find((user) => user.id === e.id)?.username}
                      </li>
                    );
                  })}
                </ul>
              ) : selectedOrg && selectedOrg.users.length === 0 ? (
                <p className="mt-[8px] text-[15px] leading-[18px] text-textSecondary">
                  No users in org
                </p>
              ) : (
                <p className="mt-[8px] text-[15px] leading-[18px] text-textSecondary">
                  Select an org
                </p>
              )}
            </div>

            <div className="flex flex-col rounded-lg border border-white/10 bg-gray-800 p-6 shadow-lg shadow-black/20">
              <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                Create org invite
              </h2>
              {selectedOrg ? (
                <>
                  <AppButton
                    onClick={createOrgInviteHandler}
                    className="mr-5 w-[200px]"
                  >
                    Create orgInvite
                  </AppButton>
                  {orgInvite ? (
                    <p className="mt-[8px] text-[15px] leading-[18px] text-textSecondary">
                      Org invite: {orgInvite}
                    </p>
                  ) : (
                    ""
                  )}
                </>
              ) : (
                <p className="mt-[8px] text-[15px] leading-[18px] text-textSecondary">
                  Select an org
                </p>
              )}
            </div>

            <div className="flex flex-col rounded-lg border border-white/10 bg-gray-800 p-6 shadow-lg shadow-black/20">
              <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                Switch Org status
              </h2>
              <div className="flex w-[50%] flex-col justify-between">
                {selectedOrg ? (
                  <select
                    onChange={(e) => statusHandler(e.target.value)}
                    className="border-border mb-3 h-[41px] min-w-[230px] rounded-[4px] border bg-inputBg pl-[10px] text-textSecondary"
                  >
                    <option value="" disabled selected>
                      Select a new status
                    </option>
                    {billingStatus
                      .filter((el) => el !== selectedOrg.billingStatus)
                      .map((status) => {
                        return (
                          <option
                            key={status}
                            value={status}
                            className="text-textSecondary"
                          >
                            {status}
                          </option>
                        );
                      })}
                  </select>
                ) : (
                  <p className="mt-[8px] text-[15px] leading-[18px] text-textSecondary">
                    Select an org
                  </p>
                )}
                {selectedStatus ? (
                  <AppButton onClick={orgStatusHandler} className="ml-[10px]">
                    Switch status
                  </AppButton>
                ) : (
                  ""
                )}
              </div>
            </div>

            <div className="flex flex-col rounded-lg border border-white/10 bg-gray-800 p-6 shadow-lg shadow-black/20">
              <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                Switch org
              </h2>
              {selectedOrg && selectedUser ? (
                <>
                  <AppButton
                    onClick={switchOrgHandler}
                    className="mr-5 w-[200px]"
                  >
                    Switch org
                  </AppButton>
                  <p className="mt-[8px] text-[15px] leading-[24px] text-textSecondary">
                    Add{" "}
                    {selectedUser.username
                      ? selectedUser.username
                      : selectedUser.id}{" "}
                    to org {selectedOrg.name}
                  </p>
                </>
              ) : (
                <p className="mt-[8px] text-[15px] leading-[24px] text-textSecondary">
                  Select an org
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-2 gap-6 p-6">
            <div className="flex flex-col rounded-lg border border-white/10 bg-gray-800 p-6 shadow-lg shadow-black/20">
              <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                Delete job
              </h2>
              <div className="flex flex-row">
                <AppButton
                  onClick={jobDeleteHandler}
                  className="mr-5 w-[200px]"
                >
                  Delete job
                </AppButton>
                <input
                  type="text"
                  value={jobId}
                  className="border-border h-[41px] min-w-[230px] rounded-[4px] border bg-inputBg pl-[10px] text-textSecondary "
                  onChange={(e) => setJobId(e.target.value)}
                />
              </div>
            </div>

            <div className="flex w-full flex-col rounded-lg border border-white/10 bg-gray-800 p-6 shadow-lg shadow-black/20">
              <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                Delete org
              </h2>
              <div className="flex w-full flex-row justify-around">
                {selectedOrg ? (
                  <AppButton onClick={orgDeleteHandler} className="ml-[10px]">
                    Delete
                  </AppButton>
                ) : (
                  <p className="mt-[8px] text-[15px] leading-[24px] text-textSecondary">
                    Select an org
                  </p>
                )}
              </div>
            </div>
          </div>
          <div className="background-gray-800 flex cursor-pointer flex-col rounded-lg p-6">
            {orgData ? (
              <div>
                <h2
                  className="mb-5 cursor-pointer font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]"
                  onClick={() => setDisplayOrgData(!displayOrgData)}
                >
                  Org data {displayOrgData ? "▲" : "▼"}
                </h2>
                {displayOrgData ? (
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      {orgData.jobs.length > 0 ? (
                        <>
                          <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                            Jobs (Last {orgData.jobs.length} )
                          </h2>
                          <div className="h-[500px] overflow-y-auto p-4">
                            {orgData.jobs.map((jobs) => {
                              return (
                                <AppCode
                                  language="json"
                                  code={JSON.stringify(jobs, null, 2)}
                                  key={jobs.id}
                                />
                              );
                            })}
                          </div>
                        </>
                      ) : (
                        ""
                      )}
                    </div>
                    <div>
                      {orgData.recipes.length > 0 ? (
                        <>
                          <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                            Recipes ( {orgData.recipes.length} )
                          </h2>
                          <div className="h-[500px] overflow-y-auto p-4">
                            {orgData.recipes.map((recipe) => {
                              return (
                                <AppCode
                                  language="json"
                                  code={JSON.stringify(recipe, null, 2)}
                                  key={recipe.id}
                                />
                              );
                            })}
                          </div>
                        </>
                      ) : (
                        ""
                      )}
                    </div>
                    <div>
                      {orgData.campaigns.length > 0 ? (
                        <>
                          <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                            Campaigns ( {orgData.campaigns.length} )
                          </h2>
                          <div className="h-[500px] overflow-y-auto p-4">
                            {orgData.campaigns.map((campaign) => {
                              return (
                                <AppCode
                                  language="json"
                                  code={JSON.stringify(campaign, null, 2)}
                                  key={campaign.id}
                                />
                              );
                            })}
                          </div>
                        </>
                      ) : (
                        ""
                      )}
                    </div>
                    <div>
                      {orgData.recurringJobs.length > 0 ? (
                        <>
                          <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                            Recurring jobs ( {orgData.recurringJobs.length} )
                          </h2>
                          <div className="h-[500px] overflow-y-auto p-4">
                            {orgData.recurringJobs.map((recurringJob) => {
                              return (
                                <AppCode
                                  language="json"
                                  code={JSON.stringify(recurringJob, null, 2)}
                                  key={recurringJob.id}
                                />
                              );
                            })}
                          </div>
                        </>
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                ) : (
                  ""
                )}
              </div>
            ) : !selectedOrg ? (
              <>
                <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
                  Org data
                </h2>
                <p className="mt-[16px] text-[15px] leading-[24px] text-textSecondary">
                  Select an org
                </p>
              </>
            ) : (
              <>
                <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
                  Org data
                </h2>
                <p className="mt-[16px] text-[15px] leading-[24px] text-textSecondary">
                  Loading ...
                </p>
              </>
            )}
          </div>
          {
            <div className="flex flex-col rounded-lg p-6">
              <h2 className="mb-5 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
                Running jobs: {runningJobCount}
              </h2>
            </div>
          }
          {allQueuedJobs ? (
            <div
              onClick={() => setDisplayQueuedJobs(!displayQueuedJobs)}
              className="flex flex-col rounded-lg p-6"
            >
              <h2 className="mb-5 cursor-pointer font-thin tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
                Queued jobs {displayQueuedJobs ? "▲" : "▼"}
              </h2>
              {displayQueuedJobs ? (
                <div>
                  {allQueuedJobs.length > 0 ? (
                    <>
                      <h2 className="mb-2 font-thin tracking-normal text-white lg:mb-[32px] lg:text-[24px] lg:leading-[33px]">
                        Queued jobs ( {allQueuedJobs.length} )
                      </h2>
                      <div className="h-[500px] overflow-y-auto p-4">
                        {allQueuedJobs.map((jobs, index) => {
                          return (
                            <AppCode
                              language="json"
                              code={JSON.stringify(jobs, null, 2)}
                              key={`job-${index}`}
                            />
                          );
                        })}
                      </div>
                    </>
                  ) : (
                    <p className="mt-[40px] text-[15px] leading-[24px] text-textSecondary">
                      No Queued jobs found
                    </p>
                  )}
                </div>
              ) : (
                ""
              )}
            </div>
          ) : (
            <p className="mt-[40px] text-[15px] leading-[24px] text-textSecondary">
              Loading ...
            </p>
          )}
        </div>
      ) : (
        ""
      )}
    </div>
  );
}
