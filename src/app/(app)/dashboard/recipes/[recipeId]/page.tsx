// TODO FIGURE THIS OUT

import { AppPageTitle } from "@/app/components/app-page-title";
import { CreateUpdateRecipeForm } from "../CreateUpdateRecipe";

export default function EditRecipePage({
    params,
  }: {
    params: { recipeId: string };
  }) {
    const recipeId = params.recipeId

    return (
        <div className="pl-[45px] pt-[45px]">
            <AppPageTitle>Update Recipe</AppPageTitle>
  
            <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">

            <br />
            <CreateUpdateRecipeForm 
                title= "Update Recipe"
                submitLabel = "Update Recipe"
                editId={recipeId}
            />

            </p>
        </div>
    )
  }