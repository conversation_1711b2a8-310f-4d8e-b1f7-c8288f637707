"use client";
import axios from "axios";
import { useState } from "react";

import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { AppSpinner } from "@/app/components/app-spinner";

import { cn } from "@/app/helpers/cn";
import { useGetRecipes } from "@/app/services/recipes.hook";
import Link from "next/link";

export const AllRecipes = () => {
  const { data, isLoading, refetch, isRefetching } = useGetRecipes();

  const buttonDisabled = isLoading || isRefetching;

  const [isButtonLoading, setButtonLoading] = useState(false);

  const handleDelete = async (recipeId: string) => {
    try {
      setButtonLoading(true);
      const foundData = await axios({
        method: "DELETE",
        url: `/api/recipes/${recipeId}`,
      });

      refetch();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
    setButtonLoading(false);
  };

  // Create a new page to edit a recipe
  // The page will create the same identical form as create recipe
  // But it will pre-fill it
  // And it will use the ID of the RECIPE as part of the URL
  // I think this is safe because you can only edit the recipes with the exact data, which is validated
  // TODO: CHECK SERVER FOR INJECTION / COPY

  return (
    <div className="mb-[20px] mt-[50px] pr-[45px]">
      <div className="mb-[40px] flex items-center justify-between">
        <h1 className="text-[28px] leading-[33px] text-textPrimary">
          All Recipes
        </h1>

        <AppButton onClick={refetch} disabled={buttonDisabled}>
          {buttonDisabled ? <AppSpinner /> : "Force Reload"}
        </AppButton>
      </div>

      <div>
        <p className="text-[20px] text-textSecondary">
          Recipes are shown in the Jobs Page and can be used for automation
        </p>
      </div>

      {data?.length > 0 && (
        <div className="flex flex-col gap-[20px]">
          {data.map((recipe) => (
            <div
              key={recipe.id}
              className={cn(
                "py-[20px] px-[22px] justify-between bg-blockBg gradient-dark-bg rounded-t-[8px] border-divider border border-b-0 rounded-b-[8px] border-b-1"
              )}
            >
              <div className="text-[18px] leading-[21px] text-textPrimary">
                <div className="flex justify-between mb-3 mt-3">
                  <AppButton
                    disabled={isButtonLoading}
                    onClick={() => handleDelete(recipe.id)}
                  >
                    {isButtonLoading
                      ? "Loading"
                      : "Delete this recipe (cannot be reversed)"}
                  </AppButton>
                  <Link href={`/dashboard/recipes/${recipe.id}/manage`}>
                    <AppButton>Manage alerts</AppButton>
                  </Link>
                </div>
                <AppCode
                  code={JSON.stringify(recipe, null, 2)}
                  language="json"
                />
                <Link href={`/dashboard/recipes/${recipe.id}`}>
                  <AppButton>Edit this Recipe</AppButton>
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
