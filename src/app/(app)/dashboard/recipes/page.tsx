"use client";

import { PageHeader } from "@/app/components/page-header";

import { AllRecipes } from "./AllRecipes";
import { CreateUpdateRecipeForm } from "./CreateUpdateRecipe";

export default function Campaigns() {
  return (
    <div className="flex flex-col gap-6 px-[200px] py-6">
      <PageHeader
        title="Recipes"
        descriptions={[
          "Recipes are Job Configurations. They can be used as:",
          "• Presets in the Jobs Page",
          "• Template for Campaigns",
          "Recipes appear as buttons in the Jobs Page and can be used with Campaigns to automatically run on PR or Commit",
        ]}
      />

      <CreateUpdateRecipeForm />
      <AllRecipes />
    </div>
  );
}
