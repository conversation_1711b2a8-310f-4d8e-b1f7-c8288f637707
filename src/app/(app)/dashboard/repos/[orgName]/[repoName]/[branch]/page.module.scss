.section {
  padding: 24px;
}

.header {
  height: 100px;
}

.headerTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 5px;
  h2 {
    margin: 0;
  }
}

.abi {
  padding: 20px;
  margin-top: 24px;
  color: var(--fore-on-accent-primary);
  background-color: rgb(31, 31, 31);
  border-radius: 6px;
  font-size: 15px;
}
.select {
  display: flex;
  flex-direction: column;
  gap: 4px;
  .label {
    font-size: 15px;
    font-weight: bold;
    color: rgb(77, 77, 77);
  }
}

// New designs

.buttonGrid {
  display: grid;
  // 5 columns
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
}

.handlerButtonEnabled {
  font-size: 12px;
  background-color: var(--success);
}
.handlerButtonDisabled {
  font-size: 12px;
  background-color: var(--error);
}

.singleContract {
  margin-bottom: 50px;
}
