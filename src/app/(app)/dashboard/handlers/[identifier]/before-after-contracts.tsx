import type { ABIWithSelector } from "@recon-fuzz/abi-to-invariants-ts/src/types";
import type { ChangeEvent } from "react";
import { useCallback, useContext, useMemo, useState } from "react";
import { FaSearch } from "react-icons/fa";

import { AppInput } from "@/app/components/app-input";
import { AppSwitch } from "@/app/components/app-switch";
import {
  getAllViewFunctions,
  processFunctions,
} from "@/app/services/generateFuzzerContracts";
import type { abiEntry } from "@/app/types/abi.api";

import { ABIContext } from "./abi-context";

type ViewFunctionButtonProps = {
  contractName: string;
  viewFunction: ABIWithSelector;
};

const ViewFunctionButton = ({
  contractName,
  viewFunction: { selector, name },
}: ViewFunctionButtonProps) => {
  const {
    trackedViewFunctions,
    addTrackedViewFunction,
    removeTrackedViewFunction,
  } = useContext(ABIContext);

  const isViewFunctionActive = useMemo(() => {
    if (
      !trackedViewFunctions?.[contractName] ||
      !Array.isArray(trackedViewFunctions?.[contractName])
    ) {
      return false;
    }

    // It must be an array, so check for inclusion
    return trackedViewFunctions?.[contractName].includes(selector);
  }, [contractName, selector, trackedViewFunctions]);

  const onSwitchChange = useCallback(() => {
    isViewFunctionActive
      ? removeTrackedViewFunction(contractName, selector)
      : addTrackedViewFunction(contractName, selector);
  }, [
    addTrackedViewFunction,
    contractName,
    isViewFunctionActive,
    selector,
    removeTrackedViewFunction,
  ]);

  return (
    <div className="flex justify-between border border-x-0 border-t-0 border-b-divider py-[20px]">
      <span className="text-[15px] leading-[18px] text-textPrimary">
        {name}
      </span>
      <AppSwitch
        className="ml-auto mr-[16px]"
        onChange={onSwitchChange}
        enabled={isViewFunctionActive}
      />
    </div>
  );
};

type ContractViewFunctionsProps = {
  contract: abiEntry;
  viewFunctions: ABIWithSelector[];
};

const ContractFunctionRow = ({
  contract,
  viewFunctions,
}: ContractViewFunctionsProps) => {
  const { isContractActive } = useContext(ABIContext);

  const inactive =
    !isContractActive(contract.name) || viewFunctions.length === 0;

  if (inactive) {
    return null;
  }

  return (
    <div className="mb-[20px]">
      <h2 className="my-[20px] text-[22px] leading-[26px] text-textPrimary">
        {contract.name}
      </h2>

      {viewFunctions.map((viewFunction) => (
        <ViewFunctionButton
          key={viewFunction.selector}
          contractName={contract.name}
          viewFunction={viewFunction}
        />
      ))}
    </div>
  );
};

export const BeforeAfterContracts = () => {
  const [search, setSearch] = useState("");

  const onSearchChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  }, []);

  const { filteredAbiData } = useContext(ABIContext);

  const contracts = useMemo(
    () =>
      Array.isArray(filteredAbiData?.abiData) ? filteredAbiData.abiData : [],
    [filteredAbiData?.abiData],
  );

  return (
    <div className="w-[100%]">
      <AppInput
        value={search}
        onChange={onSearchChange}
        icon={FaSearch}
        disableError
        containerClassName="w-[100%] mb-[20px]"
        placeholder="Search"
      />
      {contracts
        .filter((contract: abiEntry) => {
          return contract.name.toLowerCase().includes(search.toLowerCase());
        })
        .map((contract: abiEntry) => (
          <ContractFunctionRow
            viewFunctions={processFunctions(getAllViewFunctions(contract.abi))}
            key={contract.name}
            contract={contract}
          />
        ))}
    </div>
  );
};
