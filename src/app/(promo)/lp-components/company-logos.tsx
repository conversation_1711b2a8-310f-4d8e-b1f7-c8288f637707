import Image from "next/image";
import Link from "next/link";

export default function CompanyLogos() {
  return (
    <div className="mt-4 flex size-full flex-col rounded-lg bg-gradient-to-r from-[rgba(30,13,66,0.65)] to-[rgba(23,23,23,0.65)] p-[16px] backdrop-blur-[3.85px]">
      <div className="mb-4 flex size-full flex-col items-center justify-center text-center lg:mb-0 lg:flex-row lg:items-start lg:justify-start">
        <p className="mb-4 w-full text-center text-[24px] font-thin leading-[24px] text-white">
          Helping these projects deploy safely
        </p>
      </div>
      <div className="flex w-full flex-row items-center justify-around md:flex-wrap">
        <Link href="https://youtu.be/AT3fMhPDZFU" target="_blank">
          <Image
            src="/centrifuge-logo.svg"
            alt="Centrifuge logo"
            width={100}
            height={30}
            className="mr-0"
          />
        </Link>
        <Link href="https://youtu.be/3pvWq_zBauY" target="_blank">
          <Image
            src="/badger-logo.svg"
            alt="Badger logo"
            width={100}
            height={30}
            className="mr-0"
          />
        </Link>
        <Link href="" target="_blank">
          <Image
            src="/Corn.png"
            alt="Corn Logo"
            width={100}
            height={30}
            className="mr-0"
          />
        </Link>
        <Link href="" target="_blank">
          <Image
            src="/Liquity.svg"
            alt="Liquity Logo"
            width={30}
            height={30}
            className="mr-0"
          />
        </Link>
        <Link href="" target="_blank">
          <Image
            src="/Balancer.svg"
            alt="Balancer Logo"
            width={30}
            height={30}
            className="mr-0"
          />
        </Link>
        <Link href="" target="_blank">
          <Image
            src="/credit-coop-logo.png"
            alt="Credit coop Logo"
            width={100}
            height={40}
            className="mr-0"
          />
        </Link>
      </div>
    </div>
  );
}
