import Caroussel from "../../../components/Caroussel/Caroussel";
import Testimonial from "./Testimonial/Testimonial";

const TestimonialsList = [
  {
    customer: "Centrifuge",
    feedback: `
We worked with the phenomenal
@getreconxyz
 team to get invariant testing coverage for the
@centrifuge
  ERC-7540 implementation, leveraging their Recon Pro platform.
  Looking forward to seeing where
@getreconxyz
 goes next, they have ambitious plans to take invariant testing to the next level!
    `,
    title: "CT<PERSON>",
    name: "<PERSON><PERSON><PERSON>",
    image: "/Jeroen.jpg",
  },
  {
    customer: "Liquity",
    feedback: `We first met <PERSON> during some excellent security discussions re: Liquity v1. The breadth and depth of his audit of our v2 exceeded expectations - particularly impressive for a solo effort. He uncovered several technical, arithmetic and economic issues and discussed them with us in detail. I’d highly recommend his manual reviews for any DeFi team that takes their security seriously.`,
    title: "Cofounder",
    name: "<PERSON>",
    image: "/Liquity.svg",
  },
  {
    customer: "Badger DAO",
    feedback: `
Recon has allowed us to speed up the development of invariant tests immensely. We are able to create and execute test suites in the cloud effortlessly with virtually no boilerplate code. I highly recommend using Recon to automate your fuzzing setup.
    `,
    title: "<PERSON> <PERSON>",
    name: "<PERSON>",
    image: "/jwei.webp",
  },
  {
    customer: "<PERSON>rn",
    feedback:
      "The recon team is continuously innovating to make invariant testing vastly more accessible to projects, which makes a meaningful difference in security outcomes. They bring their broad expertise in and passion for web3 security to any engagement above and beyond the specified scope.",
    title: "Cofounder",
    name: "Dapp",
    image: "/Dapp.webp",
  },
  {
    customer: "Onchainification",
    feedback: `
Engaging with Alex&Lourens showed great proactiveness to answer deep-technical Qs along the process and help to discuss together items that require deeper drilling on invariants, really valuable not only their ability to answer Qs, but educating you on the process and empowering your team for better understanding of invariants on your architecture!`,
    // NOTE: Rest of comment `As an extra points these guys are not sleeping enough and putting every week content in YT to educate all your core devs to protect better your codebase. Chat with them and you will have better sleep at night :). Safe coding!`
    title: "Cofounder",
    name: "Petrovska",
    image: "/Petro.png",
  },
  {
    customer: "Solidity Labs",
    feedback: `
Alex has an incredible eye for detail, leaving no stone unturned during our audit. His review helped us gain confidence in our codebase before going to a competitive audit. I would highly recommend Alex’s security services to anyone building smart contract systems.`,
    title: "Founder",
    name: "Elliot",
    image: "/Elliot.jpg",
  },
  {
    customer: "Balancer DAO",
    feedback: `We worked together with Alex to review a Safe module for our DAO multi-sig. We really valued his proactiveness and quick response times so that we could ship in a timely manner. 🚀`,
    title: "Balancer Maxis",
    name: "Xeonus",
    image: "/Xeonus.jpg",
  },
  {
    customer: "Credit Coop",
    feedback: `The ROI on our engagement with Recon was extremely high. They built an invariant test suite that uncovered hard-to-spot high-severity issues and gave us a powerful tool to ship with confidence.

Moving forward, invariant testing will be core to our smart contract development at Credit Coop. When we do our next audit, Recon will have to be a part of the picture.`,
    title: "Cofonder & CTO",
    name: "Thomas Hepner",
    image: "/Thomas.jpg",
  },
  {
    customer: "Quill Finance",
    feedback: `Alex had the most transparent and interactive process of any auditor I've worked with. He was very proactive and provided in-depth analysis even beyond what was initially requested. In the end, our team was able to iterate on findings a lot quicker than we'd estimated, while also learning a huge amount along the way`,
    title: "Cofonder & CTO",
    name: "Naps62.eth",
    image: "/Naps.jpg",
  },
  //   {
  //     customer: "GuardianAudits",
  //     feedback: `
  // Recon makes it easy to fuzz in the cloud. No cloud management, simple CI / CD, and great support. I went from a template to fuzzing on the cloud within minutes.`,
  //     title: "Cofounder",
  //     name: "Danny",
  //     image: "/Petro.png",
  //   },
];

export default function Testimonials() {
  return (
    <section
      id="testimonials"
      className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
    >
      <h3 className=" sub-title-custom w-[650px] items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
        Testimonials
      </h3>
      <h4 className="mb-[40px] font-thin tracking-normal text-white md:mb-5 lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
        Our services are tailored to our customers
      </h4>
      <Caroussel data={TestimonialsList}>
        {TestimonialsList.map((testimonial, index) => {
          return (
            <Testimonial
              key={index}
              customer={testimonial.customer}
              feedback={testimonial.feedback}
              title={testimonial.title}
              name={testimonial.name}
              image={testimonial.image}
            />
          );
        })}
      </Caroussel>
    </section>
  );
}
