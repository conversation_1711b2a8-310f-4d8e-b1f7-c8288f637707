import React from "react";
import Image from "next/image";

interface TestimonialProps {
  customer: string;
  feedback: string;
  title: string;
  name: string;
  image: string;
}

export default function Testimonial({
  customer,
  feedback,
  title,
  name,
  image,
}: TestimonialProps) {
  return (
    <div className={"relative pt-10 "}>
      <div className="absolute left-1/2 -translate-x-1/2 -translate-y-1/2">
        <Image src={image} alt="company logo" height={72} width={72} />
      </div>
      <div className="mr-7 flex h-full w-[300px]  flex-col items-center justify-between overflow-visible rounded-lg border border-[#D0BCFF] bg-[#6750A41F] p-4 text-center md:w-[370px] lg:w-[370px]">
        <div className="mt-10 flex flex-col">
          <p className="items-start text-[24px] font-thin leading-[24px] text-white">
            {feedback}
          </p>
        </div>

        <div className="mt-8">
          <h3 className="text-[28px] text-white">{customer}</h3>
          <p className="text-[20px] font-thin text-[#F2F2F2]">
            {name} - {title}
          </p>
        </div>
      </div>
    </div>
  );
}
