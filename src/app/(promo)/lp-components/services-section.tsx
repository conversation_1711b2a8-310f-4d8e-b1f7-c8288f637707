import Service from "../../components/Service/Service";

interface ServicesSectionProps {
  containerWidth?: "full" | "4/5";
}

export default function ServicesSection({
  containerWidth = "4/5",
}: ServicesSectionProps) {
  const containerClass =
    containerWidth === "full"
      ? "mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:pt-[180px]"
      : "mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]";

  return (
    <section id="services" className={containerClass}>
      <Service />
    </section>
  );
}
