"use client";

import Image from "next/image";
import Link from "next/link";
import { AppButton } from "../../components/app-button";
import Testimonials from "../lp-components/Testimonials/Testimonials";
import Team from "../lp-components/Team/Team";
import Service from "../../components/Service/Service";
import Media from "../../components/Media/Media";
import Footer from "../../components/Footer/Footer";
import Navbar from "../../components/Navbar/Navbar";
import { GradientWrapper } from "../../components/gradient-wrapper";
import TvlBenefits from "../../components/TvlBenefits/TvlBenfits";
import SubstackList from "../../components/Substack/Substack";

import { redirect } from "next/navigation";

export default function Home() {
  redirect("https://book.getrecon.xyz/opsec/main.html");
  return (
    <div>
      <Navbar />
      <div className="main-container w-full overflow-x-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-b from-transparent via-transparent to-black">
          <GradientWrapper />
        </div>
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              Smart Contract Governance Setup, Review and Ongoing Monitoring
            </h1>
            <h2 className="mb-5 text-center font-thin tracking-normal text-white lg:my-[37px] lg:text-[37px] lg:leading-[33px]">
              Have one Auditor review all your Multisig Transactions before it's
              too late
            </h2>
            <Link
              href="https://tally.so/r/3jpV6E"
              className="m-0 flex flex-row items-center justify-center p-0 text-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              <AppButton variant="secondary" className="m-0 p-0">
                Start a conversation
              </AppButton>
            </Link>

            <div className="flex size-full flex-col rounded-lg bg-gradient-to-r from-[rgba(30,13,66,0.65)] to-[rgba(23,23,23,0.65)] p-[16px] backdrop-blur-[3.85px]">
              <div className="mb-4 flex size-full flex-col items-center justify-center text-center lg:mb-0 lg:flex-row lg:items-start lg:justify-start">
                <p className="mb-4 w-full text-center text-[24px] font-thin leading-[24px] text-white">
                  Helping these projects deploy safely
                </p>
              </div>
              <div className="flex w-full flex-row items-center justify-around md:flex-wrap">
                <Link href="https://youtu.be/AT3fMhPDZFU" target="_blank">
                  <Image
                    src="/centrifuge-logo.svg"
                    alt="Centrifuge logo"
                    width={100}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="https://youtu.be/3pvWq_zBauY" target="_blank">
                  <Image
                    src="/badger-logo.svg"
                    alt="Badger logo"
                    width={100}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="" target="_blank">
                  <Image
                    src="/Corn.png"
                    alt="Corn Logo"
                    width={100}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="" target="_blank">
                  <Image
                    src="/Liquity.svg"
                    alt="Liquity Logo"
                    width={30}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="" target="_blank">
                  <Image
                    src="/Balancer.svg"
                    alt="Balancer Logo"
                    width={30}
                    height={30}
                    className="mr-0"
                  />
                </Link>
                <Link href="" target="_blank">
                  <Image
                    src="/credit-coop-logo.png"
                    alt="Credit coop Logo"
                    width={100}
                    height={40}
                    className="mr-0"
                  />
                </Link>
              </div>
            </div>
          </section>

          <section
            id="stats"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Resources for Founders
            </h3>
            <blockquote className="twitter-tweet" data-media-max-width="560">
              <p lang="en" dir="ltr">
                The Right Way to Multisig
                <br />
                <br />1 hour and 40 minutes going deep on vulnerabilities tied
                to Governance and Multisigs
                <br />
                <br />
                Providing a plethora of tools to mitigate this
                <br />- Stop leaking your Safe Txs info
                <br />- Protect yourself against wrench attacks
                <br />- How to not get phished when signing Safe…{" "}
                <a href="https://t.co/V8sJZktmwq">pic.twitter.com/V8sJZktmwq</a>
              </p>
              &mdash; Recon (@getreconxyz){" "}
              <a href="https://twitter.com/getreconxyz/status/1885249716386226572?ref_src=twsrc%5Etfw">
                January 31, 2025
              </a>
            </blockquote>{" "}
            <script
              async
              src="https://platform.twitter.com/widgets.js"
              charSet="utf-8"
            ></script>
            <blockquote className="twitter-tweet">
              <p lang="zxx" dir="ltr">
                <a href="https://t.co/vlk30uTJn1">https://t.co/vlk30uTJn1</a>
              </p>
              &mdash; Recon (@getreconxyz){" "}
              <a href="https://twitter.com/getreconxyz/status/1922200841865363492?ref_src=twsrc%5Etfw">
                May 13, 2025
              </a>
            </blockquote>{" "}
            <script
              async
              src="https://platform.twitter.com/widgets.js"
              charSet="utf-8"
            ></script>{" "}
          </section>
          <section
            id="stats"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              TVL Protected
            </h3>
            <TvlBenefits />
          </section>

          <section
            id="testimonials"
            className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom w-[650px] items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Testimonials
            </h3>
            <h4 className="mb-[40px] font-thin tracking-normal text-white md:mb-5 lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
              Our services are tailored to our customers
            </h4>
            <Testimonials />
          </section>

          <section
            id="team"
            className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom w-[250px] items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              TEAM
            </h3>
            <Team />
          </section>

          <section
            id="services"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <Service />
          </section>

          <section
            id="media"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <div className="flex w-full flex-col items-center justify-between lg:flex-row">
              <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px] ">
                Tutorials
              </h3>
              <Link href="/media" className="m-0 p-0">
                <AppButton variant="secondary" className="m-0 p-0">
                  View all videos &gt;
                </AppButton>
              </Link>
            </div>
            <Media short={true} />
          </section>
          <section
            id="substack"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <div className="mb-[40px] flex w-full flex-col items-center justify-between lg:flex-row">
              <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px] ">
                Substack posts
              </h3>
              <Link
                href="https://getrecon.substack.com/"
                className="mb-5 text-center"
              >
                <AppButton variant="secondary" size="lg">
                  Read more &gt;
                </AppButton>
              </Link>
            </div>
            <SubstackList />
          </section>
        </main>
        <div className="w-full border-t border-t-[#FFFFFF]">
          <Footer />
        </div>
      </div>
    </div>
  );
}
