import { useState } from "react";
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { cn } from "@/lib/utils";

interface CarousselProps {
  data: any[];
  children: React.ReactNode;
  className?: string;
}

// Constants for carousel configuration
const CAROUSEL_CONFIG = {
  BUTTON_SIZE: 24,
  TRANSFORM_STEP: 20,
  TRANSITION_DURATION: 300,
} as const;

// Reusable carousel button component
interface CarouselButtonProps {
  onClick: () => void;
  direction: "prev" | "next";
  className?: string;
}

function CarouselButton({
  onClick,
  direction,
  className,
}: CarouselButtonProps) {
  const Icon = direction === "prev" ? FiChevronLeft : FiChevronRight;
  const positionClass = direction === "prev" ? "right-12" : "right-0";

  return (
    <button
      onClick={onClick}
      className={cn(
        "absolute top-[-50px] z-10 rounded-full border border-accent-primary bg-back-neutral-secondary p-2 text-fore-on-accent-primary transition-all duration-200 hover:bg-accent-primary hover:scale-110",
        positionClass,
        className
      )}
      aria-label={`${direction === "prev" ? "Previous" : "Next"} item`}
    >
      <Icon size={CAROUSEL_CONFIG.BUTTON_SIZE} />
    </button>
  );
}

export default function Caroussel({
  data,
  children,
  className,
}: CarousselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0
        ? data.length - Math.floor(data.length / 2)
        : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === data.length - Math.floor(data.length / 2)
        ? 0
        : prevIndex + 1
    );
  };

  return (
    <div
      className={cn(
        "scrollbar-hide relative flex w-full items-center justify-center",
        className
      )}
    >
      <CarouselButton onClick={handlePrev} direction="prev" />
      <CarouselButton onClick={handleNext} direction="next" />

      <div className="flex w-full snap-x snap-mandatory space-x-4 overflow-x-scroll">
        <div
          className="flex overflow-visible transition-transform ease-in-out"
          style={{
            transform: `translateX(-${
              currentIndex * CAROUSEL_CONFIG.TRANSFORM_STEP
            }%)`,
            transitionDuration: `${CAROUSEL_CONFIG.TRANSITION_DURATION}ms`,
          }}
        >
          {children}
        </div>
      </div>
    </div>
  );
}
