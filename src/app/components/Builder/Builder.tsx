import React from "react";
import { useGetStats } from "../../services/stats.hook";
import VideoPlayer from "../VideoPlayer/VideoPlayer";
import { AppButton } from "../app-button";
import { H3, Body3, Title2Strong } from "../app-typography";
import Link from "next/link";
import { cn } from "@/lib/utils";

const BUILDER_CONFIG = {
  DEFAULT_STATS: {
    abiDataCount: "90",
    abiJobCount: "200",
  },
  VIDEO_URL: "https://www.youtube.com/embed/8FRmFlmhvfY",
  SANDBOX_URL: "/tools/sandbox",
} as const;

const BUILDER_FEATURES = [
  {
    title: "It's never been easier",
    description:
      "2 click scaffolding a State of the Art Medusa, Echidna, Halmos and Kontrol Invariant Testing Setup",
  },
  {
    title: "Use the right tools for the job",
    description:
      "Use Foundry to develop and debug, use medusa and echidna for invariant testing",
  },
  {
    title: "Only as opinionated as necessary",
    description:
      "Compatible with any Foundry project. Zero configuration necessary",
  },
] as const;

interface StatCardProps {
  value: string | number;
  label: string;
  className?: string;
}

function StatCard({ value, label, className }: StatCardProps) {
  return (
    <div className={cn("w-auto text-center", className)}>
      <H3 className="sub-title-custom font-bold tracking-normal">{value}</H3>
      <Body3 color="primary" className="mb-5 text-white lg:mb-8">
        {label}
      </Body3>
    </div>
  );
}

interface FeatureCardProps {
  title: string;
  description: string;
  className?: string;
}

function FeatureCard({ title, description, className }: FeatureCardProps) {
  return (
    <div
      className={cn(
        "mb-2 rounded-lg bg-back-accent-quaternary p-5 lg:mb-4",
        className
      )}
    >
      <Title2Strong color="primary" className="uppercase text-white">
        {title}
      </Title2Strong>
      <Body3 color="primary" className="text-white">
        {description}
      </Body3>
    </div>
  );
}

function BuilderStats({ stats }: { stats: any }) {
  const statsData = [
    {
      value: "FREE",
      label: "For Open Source Projects",
    },
    {
      value: stats?.abiDataCount || BUILDER_CONFIG.DEFAULT_STATS.abiDataCount,
      label: "Repos built",
    },
    {
      value: stats?.abiJobCount
        ? parseInt(String(stats.abiJobCount)) * 2
        : BUILDER_CONFIG.DEFAULT_STATS.abiJobCount,
      label: "Hours saved",
    },
  ];

  return (
    <div className="grid size-full grid-cols-3 gap-2 border-b-2 border-accent-primary text-center lg:gap-10">
      {statsData.map((stat) => (
        <StatCard key={stat.label} value={stat.value} label={stat.label} />
      ))}
    </div>
  );
}

function BuilderFeatures() {
  return (
    <div className="mt-10 flex w-full flex-col">
      {BUILDER_FEATURES.map((feature) => (
        <FeatureCard
          key={feature.title}
          title={feature.title}
          description={feature.description}
        />
      ))}
    </div>
  );
}

function BuilderVideo() {
  return (
    <div className="mt-3 w-full lg:mt-10 lg:w-3/5">
      <div className="order-1 flex w-full flex-col items-center justify-center text-left lg:order-2">
        <Title2Strong color="accent" className="mb-4 font-bold uppercase">
          Check how it works
        </Title2Strong>
        <VideoPlayer link={BUILDER_CONFIG.VIDEO_URL} />
      </div>
    </div>
  );
}

export default function Builder() {
  const { data: stats } = useGetStats();

  return (
    <div className="flex flex-col items-center rounded-lg border border-accent-primary p-4 lg:p-6">
      <BuilderStats stats={stats} />
      <BuilderFeatures />
      <BuilderVideo />

      <Link
        href={BUILDER_CONFIG.SANDBOX_URL}
        className="mt-6 flex flex-row items-center justify-center"
        target="_blank"
        rel="noopener noreferrer"
      >
        <AppButton variant="secondary">Use the Free Builder</AppButton>
      </Link>
    </div>
  );
}
