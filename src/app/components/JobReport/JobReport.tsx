import {
  prepareProperties,
  sortProperties,
} from "@/app/(app)/tools/mdReportHelper";
import type { FuzzingResults } from "@recon-fuzz/log-parser";
import React from "react";
import "./page.css";

interface JobReportProps {
  fuzzer: string;
  jobStats: FuzzingResults;
  showBrokenProp: boolean;
}

export default function JobReport({
  fuzzer,
  jobStats,
  showBrokenProp,
}: JobReportProps) {
  return (
    <div className="w-full overflow-x-hidden text-white">
      <h2 className="text-2xl font-bold">Fuzzing overview</h2>
      <p>Fuzzer: {fuzzer}</p>
      <p>Duration: {jobStats.duration}</p>
      <p>Failed tests: {jobStats.failed}</p>
      <p>Passed tests: {jobStats.passed}</p>
      <br />
      {showBrokenProp ? (
        <>
          <h2 className="text-2xl font-bold">Broken properties:</h2>
          <table className="table-auto">
            <tbody>
              {jobStats.brokenProperties.map((el, index) => {
                return (
                  <tr key={index}>
                    <td className="px-4 py-2">
                      {index} - {el.brokenProperty}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </>
      ) : (
        ""
      )}
      <br />
      <h2 className="text-2xl font-bold">Properties fuzzed:</h2>
      <table className="table-auto">
        <tbody>
          {sortProperties(jobStats.results).map(
            ({ property, status, index }) => (
              <tr key={index}>
                <td className="px-4 py-2">{property}</td>
                <td className="px-4 py-2">{status}</td>
              </tr>
            )
          )}
        </tbody>
      </table>
    </div>
  );
}
