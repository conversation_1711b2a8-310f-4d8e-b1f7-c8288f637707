import { forwardRef, useId } from "react";
import type { IconType } from "react-icons/lib";
import { useTheme } from "next-themes";

import { cn } from "../helpers/cn";
import { THEME_OPTIONS } from "../services/ThemeProvider";

type Option = {
  value: string | number;
  label: string;
};

type AppSelectProps = {
  className?: string;
  defaultValue?: string | number;
  disabled?: boolean;
  onChange?: (e: any) => void;
  value?: string | number;
  options: Option[];
  placeholder?: string;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
  mode?: "dark" | "light";
};

export const AppSelect = forwardRef(
  (
    {
      onChange,
      disabled,
      value,
      defaultValue,
      options,
      placeholder = "",
      className = "",
      label,
      containerClassName = "",
      error,
      disableError = false,
      icon: Icon,
      ...rest
    }: AppSelectProps,
    ref: any
  ) => {
    const id = useId();
    const { theme: currentTheme } = useTheme();
    const isDark = currentTheme === THEME_OPTIONS.dark;

    const getSelectStyles = () => {
      const baseStyles =
        "h-[40px] rounded-[6px] px-4 w-full outline-none transition-all duration-200 font-bold text-[16px] leading-[1.375] appearance-none cursor-pointer";

      if (disabled) {
        return cn(
          baseStyles,
          isDark
            ? "bg-transparent border border-white/60 text-white/60 opacity-40"
            : "bg-transparent border border-black/60 text-black/60 opacity-40"
        );
      }

      if (error) {
        return cn(
          baseStyles,
          isDark
            ? "bg-transparent border border-red-500 text-white/60 hover:border-red-400 focus:border-red-500"
            : "bg-transparent border border-red-500 text-black/60 hover:border-red-400 focus:border-red-500"
        );
      }

      return cn(
        baseStyles,
        isDark
          ? "bg-transparent border border-white/60 text-white/60 hover:border-white focus:border-white/60"
          : "bg-transparent border border-black/60 text-black/60 hover:border-black focus:border-black/60",
        {
          "pl-[40px]": !!Icon,
        }
      );
    };

    return (
      <div className={cn("relative w-full", containerClassName)}>
        {label && (
          <label
            htmlFor={id}
            className={cn(
              "mb-[3px] block text-[15px] leading-[18px]",
              isDark ? "text-white/80" : "text-black/80"
            )}
          >
            {label}
          </label>
        )}
        <div className="relative">
          {!!Icon && (
            <Icon
              className={cn(
                "absolute left-3 top-1/2 -translate-y-1/2 size-4 pointer-events-none",
                isDark ? "text-white/60" : "text-black/60"
              )}
            />
          )}
          <select
            id={id}
            className={cn(getSelectStyles(), className)}
            value={value}
            defaultValue={defaultValue}
            onChange={onChange}
            disabled={disabled}
            ref={ref}
            {...rest}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {/* Custom dropdown arrow */}
          <div
            className={cn(
              "absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none",
              isDark ? "text-white/60" : "text-black/60"
            )}
          >
            <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
              <path
                d="M1 1.5L6 6.5L11 1.5"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        {!disableError && error && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-red-500">
            {error}
          </span>
        )}
      </div>
    );
  }
);

AppSelect.displayName = "AppSelect";
