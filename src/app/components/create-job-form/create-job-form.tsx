import { Form<PERSON>rovider, useField<PERSON>rray, useForm } from "react-hook-form";
import { FaCheckCircle } from "react-icons/fa";
import { useEffect } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import { useGetRecipes } from "@/app/services/recipes.hook";

import { AppButton } from "../app-button";
import { AppInput } from "../app-input";
import { AppSpinner } from "../app-spinner";
import { JobTypeFuzzer } from "../job-type-fuzzer";
import { SubFormEchidna } from "./subform-echidna";
import { SubFormFoundry } from "./subform-foundry";
import { SubFormHalmos } from "./subform-halmos";
import { SubFormKontrol } from "./subform-kontrol";
import { SubFormMedusa } from "./subform-medusa";

import type { JobFormProps, GitHubLinkFormValues } from "./types";
import { parseGitHubURL, applyRecipeDefaults } from "./utils";

export function CreateJobForm({
  onSubmit,
  title,
  submitLabel = "Start Job",
  hideEnv = false,
  hidePresets = false,
  env,
  setEnv,
  jobId,
  dynamicReplacement,
  buildHandler,
  setRecipeId,
}: JobFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<GitHubLinkFormValues>({
    defaultValues: {
      timeout: "3600",
      testLimit: "100000",
    },
  });
  const { data: recipes } = useGetRecipes();

  const orgName = watch("orgName");
  const repoName = watch("repoName");
  const ref = watch("ref");

  const methods = useForm<GitHubLinkFormValues>({
    defaultValues: {
      fields: [{ variableName: "", interface: "", value: "" }],
    },
  });
  const { control } = methods;

  const { fields, append, remove } = useFieldArray({
    control,
    name: "fields",
  });

  const watchedFields = watch("fields");

  useEffect(() => {
    // Filter out incomplete field groups
    if (!watchedFields) return;

    const validFields = watchedFields.filter(
      (field) =>
        field.variableName.trim() &&
        field.interface.trim() &&
        field.value.trim()
    );

    const prepContract = validFields.map((field) => ({
      target: `${field.variableName} = ${field.interface}`,
      replacement: `${field.variableName} = ${field.interface}(${field.value});`,
      endOfTargetMarker: "[^;]*",
      targetContract: "Setup.sol",
    }));
    setValue("prepareContracts", prepContract);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchedFields, methods.getValues()]);

  const githubUrlRegister = register("githubURL");

  return (
    <FormProvider
      {...({
        register,
        handleSubmit,
        setValue,
        setError,
        watch,
        errors,
        isSubmitting,
      } as any)}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <h3 className="mb-[22px] text-[28px] leading-[33px] text-textPrimary">
          {title}
        </h3>

        {!hidePresets && recipes && (
          <div className="my-[20px] flex flex-wrap gap-[20px]">
            {recipes.map((recipe) => (
              <AppButton
                onClick={() =>
                  applyRecipeDefaults(recipe, setValue, setEnv, setRecipeId)
                }
                className=" px-[14px] py-[9px] leading-[21px]"
                key={recipe.displayName}
              >
                Use Default `{recipe.displayName}`
              </AppButton>
            ))}
          </div>
        )}

        {!hideEnv && env && (
          <div className="mb-6 w-full">
            <JobTypeFuzzer value={env} onChange={(value) => setEnv(value)} />
          </div>
        )}

        <div className="flex flex-col gap-6 self-stretch">
          <div className="flex flex-col gap-6 self-stretch">
            {!buildHandler && (
              <AppInput
                label="Job Label"
                {...register("label")}
                type="text"
                defaultValue=""
              />
            )}

            <AppInput
              {...githubUrlRegister}
              onChange={(e) => {
                githubUrlRegister.onChange(e);
                parseGitHubURL(e.target.value, setValue, setError);
              }}
              type="text"
              label="GitHub Repo URL"
              placeholder="Enter GitHub Repo URL"
              error={errors.githubURL?.message}
            />
          </div>

          <div className="h-px w-full bg-white/10" />

          <div className="flex flex-col gap-4 self-stretch">
            <h3 className="text-xl font-bold leading-[1.3] text-[#F5F5F5]">
              Or specify the organization, repository and branch directly
            </h3>

            <div className="flex w-full flex-col gap-6 self-stretch">
              <div className="flex gap-6">
                <AppInput
                  label="Organization"
                  {...register("orgName")}
                  type="text"
                />
                <AppInput {...register("repoName")} type="text" label="Repo" />
              </div>

              <div className="flex flex-row gap-6 self-stretch">
                <AppInput {...register("ref")} type="text" label="Branch " />
                <AppInput
                  {...register("directory")}
                  type="text"
                  label="Directory"
                />
              </div>
            </div>

            {!env && (
              <AppInput
                {...register("customOut")}
                type="text"
                label="Custom output folder"
              />
            )}
            {dynamicReplacement && (
              <h3 className="mb-4 text-base leading-[19px] text-textPrimary">
                Dynamic replacement
              </h3>
            )}

            {dynamicReplacement &&
              fields.map((field, index) => (
                <div
                  key={field.id}
                  className="mb-4 flex items-center gap-6 rounded border-b-orange-50"
                >
                  <AppInput
                    className="mb-2"
                    label="Variable Name"
                    {...register(`fields.${index}.variableName` as const)}
                    type="text"
                    defaultValue={field.variableName}
                  />
                  <AppInput
                    className="mb-2"
                    label="Interface"
                    {...register(`fields.${index}.interface` as const)}
                    type="text"
                    defaultValue={field.interface}
                  />
                  <AppInput
                    className="mb-2"
                    label="Value"
                    {...register(`fields.${index}.value` as const)}
                    type="text"
                    defaultValue={field.value}
                  />
                  <button
                    type="button"
                    onClick={() => remove(index)}
                    className=" size-[18px] text-accent-primary underline"
                    title="Delete Field Group"
                  >
                    Remove
                  </button>
                </div>
              ))}

            {dynamicReplacement && (
              <AppButton
                type="button"
                onClick={() =>
                  append({ variableName: "", interface: "", value: "" })
                }
                className="mt-4 rounded p-2 text-white transition-colors"
              >
                Add More Fields
              </AppButton>
            )}
          </div>

          <div className="h-px w-full bg-white/10" />

          {!hideEnv && env && (
            <div className="flex flex-col gap-4 self-stretch">
              <h3 className="text-xl font-bold leading-[1.3] text-[#F5F5F5]">
                Configure custom parameters for {env.toLowerCase()}:
              </h3>

              <div className="flex flex-col gap-8 self-stretch">
                {env === ENV_TYPE.MEDUSA ? (
                  <SubFormMedusa />
                ) : env === ENV_TYPE.ECHIDNA ? (
                  <SubFormEchidna />
                ) : env === ENV_TYPE.FOUNDRY ? (
                  <SubFormFoundry />
                ) : env === ENV_TYPE.HALMOS ? (
                  <SubFormHalmos />
                ) : (
                  <SubFormKontrol />
                )}
              </div>
            </div>
          )}
          <div className="h-px w-full bg-white/10" />

          <AppButton
            type="submit"
            disabled={isSubmitting}
            size="lg"
            fullWidth
            className="flex flex-row items-center justify-center gap-1 self-stretch rounded-lg px-3 py-2"
          >
            {isSubmitting ? <AppSpinner /> : submitLabel}
          </AppButton>
          {!!jobId && (
            <div className="flex flex-col gap-4 self-stretch">
              <h3 className="text-xl font-bold leading-[1.3] text-[#F5F5F5]">
                Job Details:
              </h3>
              <div className="flex items-center justify-center gap-3 rounded-lg bg-success p-4 text-lg leading-[21px] text-textPrimary">
                <FaCheckCircle className="size-5 text-textPrimary" />
                Job has been successfully created
              </div>
              <p className="mb-4 text-base leading-[18px] text-textPrimary">
                ID: {jobId}
              </p>

              {[
                `Org: ${orgName ?? ""}`,
                `Repo: ${repoName ?? ""}`,
                `Branch: ${ref ?? ""}`,
                `Directory: ${watch("directory") ?? ""}`,
                `Custom Out: ${watch("customOut") ?? ""}`,
              ]
                .filter(Boolean)
                .map((line) => (
                  <p
                    key={line}
                    className="mb-1 w-full border-b border-divider pb-4 text-base leading-[18px] text-textSecondary"
                  >
                    {line}
                  </p>
                ))}
            </div>
          )}
        </div>
      </form>
    </FormProvider>
  );
}
