// Form field options
export const VERBOSITY_OPTIONS = [
  { label: "-v", value: "-v" },
  { label: "-vv", value: "-vv" },
  { label: "-vvv", value: "-vvv" },
  { label: "-vvvv", value: "-vvvv" },
  { label: "-vvvvv", value: "-vvvvv" },
];

export const FOUNDRY_VERBOSITY_OPTIONS = VERBOSITY_OPTIONS.slice(1); // Remove -v option for Foundry

export const FORK_MODE_OPTIONS = [
  { label: "Non-Forked", value: "NONE" },
  { label: "Mainnet", value: "MAINNET" },
  { label: "Optimism", value: "OPTIMISM" },
  { label: "Arbitrum", value: "ARBITRUM" },
  { label: "Polygon", value: "POLYGON" },
  { label: "Base", value: "BASE" },
  { label: "Custom", value: "CUSTOM" },
];

export const TEST_COMMAND_OPTIONS = [
  { label: "None", value: "" },
  { label: "Match Test", value: "--match-test" },
];

export const TEST_MODE_OPTIONS = [
  { label: "Use config", value: "config" },
  { label: "exploration", value: "exploration" },
  { label: "assertion", value: "assertion" },
  { label: "property", value: "property" },
  { label: "optimization", value: "optimization" },
  { label: "overflow", value: "overflow" },
];

export const PREPROCESS_OPTIONS = [
  {
    label: "No preprocess",
    value: "",
  },
  {
    label: "yarn install --ignore-scripts",
    value: "yarn install --ignore-scripts",
  },
  {
    label: "forge install",
    value: "forge install",
  },
  {
    label: "npm install",
    value: "npm install",
  },
];

// Default form values
export const DEFAULT_FORM_VALUES = {
  timeout: "3600",
  testLimit: "100000",
  forkBlock: "LATEST",
  verbosity: "-vvv",
  ref: "main",
};

// Styling constants
export const FORM_STYLES = {
  inputGroup: "mb-6 flex flex-wrap gap-6",
  input: "mb-[8px] min-w-[200px]",
  divider: "h-px w-full bg-white/10",
  sectionTitle: "text-xl font-bold leading-[1.3] text-[#F5F5F5]",
  formTitle: "mb-[22px] text-[28px] leading-[33px] text-textPrimary",
} as const;
