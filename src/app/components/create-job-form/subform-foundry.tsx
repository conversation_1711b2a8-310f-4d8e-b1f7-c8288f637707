import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelect } from "../app-select";
import { PreInstallItem } from "./preinstall-item";
import {
  FORM_STYLES,
  FOUNDRY_VERBOSITY_OPTIONS,
  FORK_MODE_OPTIONS,
  TEST_COMMAND_OPTIONS,
} from "./constants";

export const SubFormFoundry = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");
  const testCommand = watch("testCommand");

  useEffect(() => {}, [forkMode, testCommand, setValue]);

  return (
    <div className="flex flex-col gap-8 self-stretch">
      <div className={FORM_STYLES.inputGroup}>
        <AppInput
          className={FORM_STYLES.input}
          label="Tester Contract Name"
          {...register("contract")}
          type="text"
          placeholder="InvariantTest"
        />

        <AppInput
          className={FORM_STYLES.input}
          label="Runs"
          {...register("runs")}
          type="text"
          placeholder="256"
        />
      </div>

      <div className={FORM_STYLES.inputGroup}>
        <AppInput
          className={FORM_STYLES.input}
          label="Seed"
          {...register("seed")}
          type="text"
          placeholder="Optional: Random seed"
        />

        <AppSelect
          className={FORM_STYLES.input}
          label="Select test command"
          {...register("testCommand")}
          options={TEST_COMMAND_OPTIONS}
        />
      </div>

      {testCommand && testCommand !== "" && (
        <div className={FORM_STYLES.inputGroup}>
          <AppInput
            className={FORM_STYLES.input}
            label="Target Test"
            {...register("testTarget")}
            type="text"
            placeholder="test_invariant_*"
          />
        </div>
      )}

      <div className={FORM_STYLES.inputGroup}>
        <AppSelect
          className={FORM_STYLES.input}
          label="Select verbosity"
          {...register("verbosity")}
          options={FOUNDRY_VERBOSITY_OPTIONS}
        />

        <AppSelect
          className={FORM_STYLES.input}
          label="Select Fork Mode"
          {...register("forkMode")}
          options={FORK_MODE_OPTIONS}
        />
      </div>

      {forkMode && forkMode === "CUSTOM" && (
        <div className={FORM_STYLES.inputGroup}>
          <AppInput
            className={FORM_STYLES.input}
            label="RPC URL"
            {...register("rpcUrl")}
            type="text"
            placeholder="https://mainnet.infura.io/v3/..."
          />
        </div>
      )}

      {forkMode && forkMode !== "NONE" && (
        <div className={FORM_STYLES.inputGroup}>
          <AppInput
            className={FORM_STYLES.input}
            label="Fork Block"
            {...register("forkBlock")}
            type="text"
            placeholder="LATEST"
          />
        </div>
      )}

      <div className={FORM_STYLES.inputGroup}>
        <PreInstallItem />
      </div>
    </div>
  );
};
