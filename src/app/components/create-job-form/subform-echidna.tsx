import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelect } from "../app-select";
import { AppCheckbox } from "../app-checkbox";
import { PreInstallItem } from "./preinstall-item";
import { FORM_STYLES, FORK_MODE_OPTIONS, TEST_MODE_OPTIONS } from "./constants";

export const SubFormEchidna = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");

  useEffect(() => {}, [forkMode, setValue]);

  return (
    <>
      <div className={FORM_STYLES.inputGroup}>
        <AppInput
          className={FORM_STYLES.input}
          label="Path to test contract"
          {...register("pathToTester")}
          type="text"
          placeholder="test/invariants/CryticTester.sol"
        />

        <AppInput
          className={FORM_STYLES.input}
          label="Echidna config filename"
          {...register("echidnaConfig")}
          type="text"
          placeholder="echidna.yaml"
        />
      </div>

      <div className={FORM_STYLES.inputGroup}>
        <AppInput
          className={FORM_STYLES.input}
          label="Tester Contract Name"
          {...register("contract")}
          type="text"
          placeholder="CryticTester"
        />

        <AppInput
          className={FORM_STYLES.input}
          label="Corpus Dir"
          {...register("corpusDir")}
          type="text"
          placeholder="echidna"
        />
      </div>
      <div className="mb-6 flex flex-wrap gap-6">
        <AppInput
          className="mb-[8px] min-w-[200px]"
          label="Test Limit"
          {...register("testLimit")}
          type="text"
        />

        <AppSelect
          className={FORM_STYLES.input}
          label="Select Mode"
          {...register("testMode", { required: "Mode is required" })}
          options={TEST_MODE_OPTIONS}
        />
      </div>
      <div className="mb-6 flex flex-wrap gap-6">
        <AppInput
          className="mb-[8px] min-w-[200px]"
          label="Corpus Re-use Job ID"
          {...register("targetCorpus")}
          type="text"
        />

        <AppSelect
          className={FORM_STYLES.input}
          label="Select Fork Mode"
          {...register("forkMode")}
          options={FORK_MODE_OPTIONS}
        />
      </div>
      <div className="mb-6 flex flex-wrap gap-6">
        {forkMode && forkMode === "CUSTOM" && (
          <AppInput
            className="mb-[8px]"
            label="RPC URL"
            {...register("rpcUrl")}
            type="text"
            defaultValue=""
          />
        )}

        {forkMode && forkMode !== "NONE" && (
          <AppInput
            className="mb-[8px]"
            label="Fork Block"
            {...register("forkBlock")}
            type="text"
            defaultValue="LATEST"
          />
        )}
      </div>

      {forkMode && forkMode !== "NONE" && (
        <AppCheckbox
          className="mb-[8px]"
          label="Dynamic Block Replacement"
          {...register("forkReplacement")}
          tooltip="This allows Recon to dynamically replace the fork block and timestamp in your tester. Requires the use of Recon specific tags."
        />
      )}

      <PreInstallItem />
    </>
  );
};
