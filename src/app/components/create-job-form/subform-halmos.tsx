import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelect } from "../app-select";
import { PreInstallItem } from "./preinstall-item";
import { FORM_STYLES, VERBOSITY_OPTIONS } from "./constants";

export const SubFormHalmos = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");

  useEffect(() => {}, [forkMode, setValue]);

  return (
    <div className="flex flex-col gap-8 self-stretch">
      <div className={FORM_STYLES.inputGroup}>
        <AppInput
          className={FORM_STYLES.input}
          label="Tester Contract Name"
          {...register("contract")}
          type="text"
          placeholder="InvariantTest"
        />

        <AppInput
          className={FORM_STYLES.input}
          label="Tester Function Prefix"
          {...register("halmosPrefix")}
          type="text"
          placeholder="check_"
        />
      </div>

      <div className={FORM_STYLES.inputGroup}>
        <AppInput
          className={FORM_STYLES.input}
          label="Array Lengths"
          {...register("halmosArray")}
          type="text"
          placeholder="2"
        />

        <AppInput
          className={FORM_STYLES.input}
          label="Loops"
          {...register("halmosLoops")}
          type="text"
          placeholder="2"
        />
      </div>

      <div className={FORM_STYLES.inputGroup}>
        <AppSelect
          className={FORM_STYLES.input}
          label="Select verbosity"
          {...register("verbosity")}
          options={VERBOSITY_OPTIONS}
          defaultValue="-vvv"
        />

        <PreInstallItem />
      </div>
    </div>
  );
};
