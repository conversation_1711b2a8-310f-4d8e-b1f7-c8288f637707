import React from "react";
import { AppButton } from "../app-button";
import { H3, Body3, Title2Strong, Body2 } from "../app-typography";
import Link from "next/link";
import { FaTelegram } from "react-icons/fa";
import { cn } from "@/lib/utils";

// Constants for service configuration
const SERVICE_CONFIG = {
  TELEGRAM_URL: "https://t.me/GalloDaSballo",
} as const;

const SERVICE_STAGES = [
  {
    title: "Early Stage:",
    descriptions: [
      "Define key invariants, scaffold and maintain invariant testers",
      "Add invariants as the project grows",
    ],
  },
  {
    title: "Pre Audit Stage:",
    descriptions: [
      "Reach 100% Coverage, handout to your Auditors a full set of test repros to produce meaningful states or broken properties",
    ],
  },
  {
    title: "Solo Review Stage:",
    descriptions: [
      "Recon is made by highly respected SRs that can help you with Manual Review",
    ],
  },
  {
    title: "Audit Stage:",
    descriptions: [
      "We can support you during audits, by adding new properties flagged by your reviewers and by reproducing bugs in invariant tests as a means to ensure they are not introduced later",
      "Our cloud runners ensure you can quickly queue and test fixes, no more waiting for your engineer to come back from the weekend",
    ],
  },
] as const;

const SERVICE_OFFERS = [
  {
    title: "Manual Review",
    description:
      "A high quality review done by top Security Researchers, ideally paired with Invariant Testing",
  },
  {
    title: "Invariant Test Writing",
    description:
      "Testing written by an experienced fuzzing engineer, for projects that want to skill up their codebase, includes unlimited cloud runs during the engagement",
  },
  {
    title: "Recon Pro",
    description:
      "Cloud Fuzzing as a service, a versatile and easy way to run invariant testing in the cloud",
  },
] as const;

// Reusable components
interface ServiceStageProps {
  stage: (typeof SERVICE_STAGES)[number];
  className?: string;
}

function ServiceStage({ stage, className }: ServiceStageProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <Title2Strong color="primary" className="uppercase text-white">
        {stage.title}
      </Title2Strong>
      {stage.descriptions.map((description: string) => (
        <Body2
          key={description.slice(0, 20)}
          color="primary"
          className="text-white"
        >
          {description}
        </Body2>
      ))}
    </div>
  );
}

interface ServiceOfferProps {
  offer: (typeof SERVICE_OFFERS)[number];
  className?: string;
}

function ServiceOffer({ offer, className }: ServiceOfferProps) {
  return (
    <li className={cn("space-y-1", className)}>
      <div className="flex items-start gap-2">
        <Body2 color="primary" className="font-bold text-white">
          {offer.title}
        </Body2>
        <Body2 color="primary" className="text-white">
          &gt;
        </Body2>
      </div>
      <Body2 color="primary" className="text-white">
        {offer.description}
      </Body2>
    </li>
  );
}

function ServiceHeader() {
  return (
    <div className="mb-6 text-center">
      <H3 className="sub-title-custom mb-4 font-bold leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
        SERVICES
      </H3>
      <Body3
        color="primary"
        className="mb-6 text-white lg:text-[37px] lg:leading-[33px]"
      >
        Boutique Audits, we can write code and break invariants, whether you
        want us to code with you perform manual review
      </Body3>
      <Link
        href={SERVICE_CONFIG.TELEGRAM_URL}
        className="inline-flex items-center justify-center"
        target="_blank"
        rel="noopener noreferrer"
      >
        <AppButton variant="primary" rightIcon={<FaTelegram />}>
          Questions? Ask the Founder
        </AppButton>
      </Link>
    </div>
  );
}

function ServiceDetails() {
  return (
    <div className="mt-6 flex w-full flex-col items-start justify-center space-y-6 rounded-lg bg-back-neutral-secondary p-4 text-left lg:w-4/5 lg:p-6">
      <Title2Strong color="primary" className="text-white">
        We can support your team at all stages of development
      </Title2Strong>

      {SERVICE_STAGES.map((stage) => (
        <ServiceStage key={stage.title} stage={stage} />
      ))}

      <div className="space-y-4">
        <Title2Strong color="primary" className="uppercase text-white">
          Our offers:
        </Title2Strong>
        <ul className="space-y-4">
          {SERVICE_OFFERS.map((offer) => (
            <ServiceOffer key={offer.title} offer={offer} />
          ))}
        </ul>
      </div>
    </div>
  );
}

export default function Service() {
  return (
    <div className="flex flex-col items-center rounded-lg border border-accent-primary p-4 lg:p-10">
      <ServiceHeader />
      <ServiceDetails />
    </div>
  );
}
