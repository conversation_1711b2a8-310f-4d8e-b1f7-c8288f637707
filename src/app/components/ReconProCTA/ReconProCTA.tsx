import React from "react";
import { AppButton } from "../app-button";
import { H3, Body3, Title2Strong } from "../app-typography";
import Link from "next/link";
import { cn } from "@/lib/utils";

const RECON_PRO_CONFIG = {
  PRO_URL: "/pro/",
} as const;

const PRO_STATS = [
  {
    value: "22",
    label: "Pro Accounts",
  },
  {
    value: "10k+",
    label: "Jobs run in the cloud",
  },
  {
    value: "108k+",
    label: "Properties Broken",
  },
] as const;

const PRO_FEATURES = [
  {
    title: "It's never been easier",
    description:
      "3 click to run Medusa, Echidna, Halmos or Kontrol in the cloud, works with private repos",
  },
  {
    title: "One click sharing and Corpus Reuse",
    description:
      "Make your result public in one click with automatic reports and repro for all fuzzers",
  },
  {
    title: "Ready for Automation",
    description:
      "Run on PR, Commit or via API, trigger alerts on broken properties",
  },
] as const;

interface ProStatCardProps {
  value: string;
  label: string;
  className?: string;
}

function ProStatCard({ value, label, className }: ProStatCardProps) {
  return (
    <div className={cn("w-auto text-center", className)}>
      <H3 className="sub-title-custom font-bold tracking-normal">{value}</H3>
      <Body3 color="primary" className="mb-5 text-white lg:mb-8">
        {label}
      </Body3>
    </div>
  );
}

interface ProFeatureCardProps {
  title: string;
  description: string;
  className?: string;
}

function ProFeatureCard({
  title,
  description,
  className,
}: ProFeatureCardProps) {
  return (
    <div
      className={cn(
        "mb-2 rounded-lg bg-back-accent-quaternary p-5 lg:mb-4",
        className
      )}
    >
      <Title2Strong color="primary" className="uppercase text-white">
        {title}
      </Title2Strong>
      <Body3 color="primary" className="text-white">
        {description}
      </Body3>
    </div>
  );
}

function ProStats() {
  return (
    <div className="grid size-full grid-cols-3 gap-2 border-b-2 border-accent-primary text-center lg:gap-10">
      {PRO_STATS.map((stat) => (
        <ProStatCard key={stat.label} value={stat.value} label={stat.label} />
      ))}
    </div>
  );
}

function ProFeatures() {
  return (
    <div className="mt-10 flex w-full flex-col">
      {PRO_FEATURES.map((feature) => (
        <ProFeatureCard
          key={feature.title}
          title={feature.title}
          description={feature.description}
        />
      ))}
    </div>
  );
}

export default function ReconProCTA() {
  return (
    <div className="flex flex-col items-center rounded-lg border border-accent-primary p-4 lg:p-6">
      <ProStats />
      <ProFeatures />

      <Link
        href={RECON_PRO_CONFIG.PRO_URL}
        className="mt-6 flex flex-row items-center justify-center"
        target="_blank"
        rel="noopener noreferrer"
      >
        <AppButton variant="primary" size="lg">
          Learn more about Pro
        </AppButton>
      </Link>
    </div>
  );
}
