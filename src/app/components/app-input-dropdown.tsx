import { forwardRef, useId, useState, useMemo, useEffect } from "react";
import type { IconType } from "react-icons/lib";
import { cn } from "../helpers/cn";
import type { Recipe } from "../services/recipes.hook";
import type { AbiApiData } from "../types/abi.api";

type Chain = {
  id: number;
  name: string;
};

type DropdownItem = {
  id: string | number;
  label: string;
  fields: Recipe | AbiApiData | Chain;
};

type AppInputDropdownProps = {
  type?: "text" | "password";
  className?: string;
  defaultValue?: string;
  disabled?: boolean;
  onChange?: (e: any) => void;
  value?: string | number;
  placeholder?: string;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
  dropdownItems?: DropdownItem[]; // Array of objects for dropdown
  onItemSelect?: (id: string | number) => void; // Function to get the selected item's ID
};

export const AppInputDropdown = forwardRef(
  (
    {
      type,
      onChange,
      defaultValue,
      disabled,
      value,
      placeholder = "",
      className = "",
      label,
      containerClassName = "",
      error,
      icon: Icon,
      disableError = false,
      dropdownItems = [],
      onItemSelect,
      ...rest
    }: AppInputDropdownProps,
    ref: any
  ) => {
    const id = useId();
    const [searchTerm, setSearchTerm] = useState("");
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    // Filter dropdown items based on the search term
    const filteredItems = useMemo(() => {
      return dropdownItems.filter((item) =>
        Object.values(item.fields).some((value) =>
          String(value).toLowerCase().includes(searchTerm)
        )
      );
    }, [dropdownItems, searchTerm]);

    const handleSelectItem = (itemId: string | number, itemLabel: string) => {
      if (onItemSelect) {
        onItemSelect(itemId); // Pass the selected item's ID
      }
      setSearchTerm(itemLabel); // Set selected item's label in the input field
      setIsDropdownOpen(false); // Close dropdown after selection
    };

    return (
      <div className={cn("relative", containerClassName)}>
        {label && (
          <label
            htmlFor={id}
            className="mb-[3px] block text-[15px] leading-[18px] text-textSecondary"
          >
            {label}
          </label>
        )}
        {!!Icon && (
          <Icon className="absolute left-2 top-1/2 -translate-y-1/2 text-textSecondary" />
        )}
        <input
          id={id}
          className={cn(
            "h-[41px] border border-border rounded-[4px] min-w-[230px] border-divider bg-inputBg pl-[10px] w-[100%] text-textSecondary italic outline-none",
            {
              "pl-[30px]": !!Icon,
            },
            className
          )}
          autoComplete="off"
          {...{
            placeholder,
            defaultValue,
            type,
            value: searchTerm, // Bind input value to the search term
            onChange: (e) => {
              setSearchTerm(e.target.value); // Update search term as user types
              setIsDropdownOpen(true); // Open dropdown when user types
            },
            onFocus: () => setIsDropdownOpen(true), // Open dropdown on input focus
            disabled,
            ref,
            ...rest,
          }}
        />

        {isDropdownOpen && filteredItems.length > 0 && (
          <ul
            className="absolute z-10 mt-1 max-h-40 w-full overflow-auto rounded-md bg-inputBg shadow-lg"
            onMouseLeave={() => setIsDropdownOpen(false)}
            onMouseEnter={() => setIsDropdownOpen(true)}
          >
            {filteredItems.map((item) => (
              <li
                key={item.id}
                className="cursor-pointer p-2 text-gray-400 hover:border-b hover:border-divider"
                onClick={() => handleSelectItem(item.id, item.label)}
              >
                {item.label}
              </li>
            ))}
          </ul>
        )}

        {!disableError && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-error">
            {error}
          </span>
        )}
      </div>
    );
  }
);

AppInputDropdown.displayName = "AppInputDropdown";
